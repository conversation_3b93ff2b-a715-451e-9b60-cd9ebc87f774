/**
 * Příklad C++ kódu pro načtení a použití exportovaných prototypů z SBertClassifier.
 * 
 * Kompilace: g++ -std=c++17 -O3 cpp_example.cpp -o classifier
 * Použití: ./classifier
 */

#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <cmath>
#include <algorithm>
#include "model_config.h"

struct PrototypeMatrix {
    std::vector<float> data;
    int num_classes;
    int embedding_dim;
    
    PrototypeMatrix() : num_classes(0), embedding_dim(0) {}
};

struct ClassificationResult {
    int class_id;
    std::string class_name;
    float confidence;
};

/**
 * Načte raw prototypy z binárního souboru.
 */
bool load_prototypes_raw(const std::string& bin_path, const std::string& dims_path, PrototypeMatrix& matrix) {
    // Načti rozměry
    std::ifstream dims_file(dims_path);
    if (!dims_file.is_open()) {
        std::cerr << "Nelze otevřít: " << dims_path << std::endl;
        return false;
    }
    
    dims_file >> matrix.num_classes >> matrix.embedding_dim;
    dims_file.close();
    
    // Načti raw data
    std::ifstream bin_file(bin_path, std::ios::binary);
    if (!bin_file.is_open()) {
        std::cerr << "Nelze otevřít: " << bin_path << std::endl;
        return false;
    }
    
    size_t total_size = matrix.num_classes * matrix.embedding_dim;
    matrix.data.resize(total_size);
    
    bin_file.read(reinterpret_cast<char*>(matrix.data.data()), total_size * sizeof(float));
    bin_file.close();
    
    std::cout << "Načteno: " << matrix.num_classes << " tříd × " << matrix.embedding_dim << " dimenzí" << std::endl;
    return true;
}

/**
 * Načte jednoduché prototypy (s názvy tříd).
 */
bool load_prototypes_simple(const std::string& path, PrototypeMatrix& matrix, std::vector<std::string>& class_names) {
    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Nelze otevřít: " << path << std::endl;
        return false;
    }
    
    // Načti header
    file.read(reinterpret_cast<char*>(&matrix.num_classes), 4);
    file.read(reinterpret_cast<char*>(&matrix.embedding_dim), 4);
    
    // Připrav data
    size_t total_size = matrix.num_classes * matrix.embedding_dim;
    matrix.data.resize(total_size);
    class_names.resize(matrix.num_classes);
    
    // Načti třídy
    for (int i = 0; i < matrix.num_classes; i++) {
        // Načti název
        int name_len;
        file.read(reinterpret_cast<char*>(&name_len), 4);
        
        std::vector<char> name_buffer(name_len);
        file.read(name_buffer.data(), name_len);
        class_names[i] = std::string(name_buffer.begin(), name_buffer.end());
        
        // Načti embedding
        float* embedding_ptr = matrix.data.data() + i * matrix.embedding_dim;
        file.read(reinterpret_cast<char*>(embedding_ptr), matrix.embedding_dim * sizeof(float));
    }
    
    file.close();
    
    std::cout << "Načteno: " << matrix.num_classes << " tříd s názvy" << std::endl;
    for (int i = 0; i < matrix.num_classes; i++) {
        std::cout << "  " << i << ": " << class_names[i] << std::endl;
    }
    
    return true;
}

/**
 * Kosinusová podobnost mezi dvěma vektory.
 */
float cosine_similarity(const float* a, const float* b, int dim) {
    float dot_product = 0.0f;
    float norm_a = 0.0f;
    float norm_b = 0.0f;
    
    for (int i = 0; i < dim; i++) {
        dot_product += a[i] * b[i];
        norm_a += a[i] * a[i];
        norm_b += b[i] * b[i];
    }
    
    norm_a = std::sqrt(norm_a);
    norm_b = std::sqrt(norm_b);
    
    if (norm_a == 0.0f || norm_b == 0.0f) {
        return 0.0f;
    }
    
    return dot_product / (norm_a * norm_b);
}

/**
 * Klasifikace pomocí prototypů (předpokládá normalizované embeddingy).
 */
ClassificationResult classify_normalized(const float* embedding, const PrototypeMatrix& matrix, const std::vector<std::string>& class_names) {
    float best_score = -1.0f;
    int best_class = 0;
    
    for (int i = 0; i < matrix.num_classes; i++) {
        const float* prototype = matrix.data.data() + i * matrix.embedding_dim;
        
        // Pro normalizované vektory je kosinusová podobnost jen dot product
        float score = 0.0f;
        for (int j = 0; j < matrix.embedding_dim; j++) {
            score += embedding[j] * prototype[j];
        }
        
        if (score > best_score) {
            best_score = score;
            best_class = i;
        }
    }
    
    ClassificationResult result;
    result.class_id = best_class;
    result.class_name = class_names[best_class];
    result.confidence = best_score;
    
    return result;
}

/**
 * Hlavní funkce - demonstrace použití.
 */
int main() {
    std::cout << "=== SBertClassifier C++ Example ===" << std::endl;
    
    // Načti prototypy
    PrototypeMatrix matrix;
    std::vector<std::string> class_names;
    
    if (!load_prototypes_simple("prototypes_simple.bin", matrix, class_names)) {
        std::cerr << "Chyba při načítání prototypů!" << std::endl;
        return 1;
    }
    
    // Simulace text embeddingu (v reálné aplikaci by se získal z ONNX modelu)
    std::vector<float> test_embedding(matrix.embedding_dim);
    
    // Příklad: embedding podobný prvnímu prototypu
    const float* first_prototype = matrix.data.data();
    for (int i = 0; i < matrix.embedding_dim; i++) {
        test_embedding[i] = first_prototype[i] + (rand() / float(RAND_MAX) - 0.5f) * 0.1f;
    }
    
    // Normalizace test embeddingu
    float norm = 0.0f;
    for (float val : test_embedding) {
        norm += val * val;
    }
    norm = std::sqrt(norm);
    
    if (norm > 0.0f) {
        for (float& val : test_embedding) {
            val /= norm;
        }
    }
    
    // Klasifikace
    ClassificationResult result = classify_normalized(test_embedding.data(), matrix, class_names);
    
    std::cout << "\n=== Výsledek klasifikace ===" << std::endl;
    std::cout << "Třída: " << result.class_name << " (ID: " << result.class_id << ")" << std::endl;
    std::cout << "Confidence: " << result.confidence << std::endl;
    
    // Test všech prototypů
    std::cout << "\n=== Test všech prototypů ===" << std::endl;
    for (int i = 0; i < matrix.num_classes; i++) {
        const float* prototype = matrix.data.data() + i * matrix.embedding_dim;
        ClassificationResult test_result = classify_normalized(prototype, matrix, class_names);
        std::cout << "Prototyp " << class_names[i] << " -> " << test_result.class_name 
                  << " (confidence: " << test_result.confidence << ")" << std::endl;
    }
    
    return 0;
}
