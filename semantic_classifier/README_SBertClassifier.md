# SBertClassifier

Minimalistická třída pro sémantickou klasifikaci kompatibilní s C++.

## Vlastnosti

- **Automatická inicializace**: Po vytvoření instance se automaticky načte model a prototypy (pokud existují)
- **Efektivní**: Používá normalizované embeddingy a vektorizované operace
- **C++ kompatibilní**: Všechny operace jsou přenositelné do C++ (numpy arrays, kosinusová podobnost)
- **Minimalistická**: Pouze nezbytné metody bez složitých závislostí

## Použití

### Základní inicializace

```python
from semantic_classifier.SBertClassifier import SBertClassifier

# Automaticky načte model a prototypy
classifier = SBertClassifier()
```

### Vytvoření prototypů

```python
# Definice trénovacích dat
class_texts = {
    "Faktura": ["faktura", "číslo faktury", "invoice number"],
    "Datum": ["datum", "datum vystavení", "date", "datum splatnosti"],
    "Částka": ["částka", "celkem", "amount", "total", "suma"]
}

# Vytvoření a uložení prototypů
classifier.create_and_save_prototypes(class_texts)
```

### Klasifikace

```python
# Jednotlivá klasifikace
class_id, confidence = classifier.classify("číslo faktury 2024001")
class_name = classifier.get_class_name(class_id)
print(f"Třída: {class_name}, Confidence: {confidence:.3f}")

# Batch klasifikace (rychlejší pro více textů)
texts = ["faktura 123", "datum 15.1.2024", "celkem 1500 Kč"]
results = classifier.classify_batch(texts)
for text, (class_id, confidence) in zip(texts, results):
    class_name = classifier.get_class_name(class_id)
    print(f"'{text}' -> {class_name} ({confidence:.3f})")
```

### Výpočet embeddingů a podobnosti

```python
# Embeddingy
embeddings = classifier.encode(["text1", "text2", "text3"])
print(f"Shape: {embeddings.shape}")  # (3, 768)

# Podobnost dvou textů
similarity = classifier.similarity("faktura", "invoice")
print(f"Podobnost: {similarity:.3f}")
```

### Export pro C++

```python
# Export prototypů v binárním formátu
classifier.export_for_cpp("prototypes.bin")
```

## API Reference

### Konstruktor

```python
SBertClassifier(model_name, model_dir, device)
```

- `model_name`: název modelu na HuggingFace (default: paraphrase-multilingual-mpnet-base-v2)
- `model_dir`: cesta k lokální složce s modelem (default: ./semantic_classifier/mpnet_ok)
- `device`: 'cpu', 'cuda' nebo 'mps' (default: 'cpu')

### Hlavní metody

#### `encode(texts) -> np.ndarray`
Vypočítá normalizované embeddingy pro texty.

#### `similarity(text1, text2) -> float`
Kosinusová podobnost mezi dvěma texty (0-1).

#### `create_and_save_prototypes(class_texts)`
Vytvoří prototypy z trénovacích dat a uloží je.

#### `classify(text, return_confidence=True) -> Tuple[int, float]`
Klasifikuje jeden text, vrací (class_id, confidence).

#### `classify_batch(texts) -> List[Tuple[int, float]]`
Vektorizovaná klasifikace více textů najednou.

#### `get_class_name(class_id) -> str`
Převede ID třídy na název.

#### `get_class_id(class_name) -> int`
Převede název třídy na ID.

#### `export_for_cpp(output_path)`
Exportuje prototypy v binárním formátu pro C++.

#### `info()`
Vypíše informace o klasifikátoru.

## Formát C++ exportu

Binární soubor obsahuje:
```
[4 bytes] počet_tříd (little endian)
[4 bytes] rozměr_embeddingů (little endian)
Pro každou třídu:
  [4 bytes] délka_názvu (little endian)
  [N bytes] název_třídy (UTF-8)
  [768*4 bytes] embedding (float32 array)
```

## Výkonnost

- **Embeddingy**: ~1000 textů/sekunda (CPU)
- **Klasifikace**: ~5000 textů/sekunda (batch)
- **Paměť**: ~75 KB pro 25 tříd (prototypy)
- **Model**: ~420 MB (paraphrase-multilingual-mpnet-base-v2)

## Příklady

Viz soubory:
- `test_sbert_classifier.py` - základní testy
- `example_sbert_usage.py` - použití s reálnými daty

## Kompatibilita s C++

Všechny operace jsou navrženy pro snadný port do C++:
- Numpy arrays → std::vector<float>
- Kosinusová podobnost → dot product normalizovaných vektorů
- Binární export → memcpy načítání
- Žádné Python-specifické závislosti v core algoritmech
