"""
Skript pro porovnání dvou přístupů ke klasifikaci textů s použitím centroidů.

Tento skript načte testovací data a porovná výsledky klasifikace
dvěma různými metoda<PERSON>, ob<PERSON> používající centroidy kategorií:
1. <PERSON><PERSON><PERSON><PERSON>vání s centroidy kategorií
2. Metoda batch_classify (z key_classify.py)
"""

import os
import sys
import pandas as pd
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from Classifier import Classifier


def load_test_data():
    """Vyt<PERSON><PERSON><PERSON> malou testovací sadu dat s příklady pro různé kategorie."""
    test_data = [
        # Faktury
        {'text': 'faktura č.', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'daňový doklad', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'celkem s DPH', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'celkem', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'celkem bez daně', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'zaokrouhleno', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'Zaokrouhleno', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'razítko a podpis', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'Celkem', 'expected_category': 'invoice', 'is_positive': True},
        {'text': 'Jažlovice', 'expected_category': 'invoice', 'is_positive': True},
        
        # Dobropisy
        {'text': 'dobropis', 'expected_category': 'credit_note', 'is_positive': True},
        {'text': 'podpis', 'expected_category': 'credit_note', 'is_positive': True},
        
        # Smlouvy
        {'text': 'smlouva o dílo č.', 'expected_category': 'contract', 'is_positive': True},
        {'text': 'rámcová smlouva o spolupráci', 'expected_category': 'contract', 'is_positive': True},
        
        # Objednávky
        {'text': 'objednávka', 'expected_category': 'order', 'is_positive': True},
        {'text': 'čobj', 'expected_category': 'order', 'is_positive': True},
        
        # Negativní příklady
        {'text': 'náhodný text bez významu', 'expected_category': 'order', 'is_positive': True},
        {'text': 'dokumentace k projektu', 'expected_category': 'order', 'is_positive': True},
        {'text': 'prezentace výsledků', 'expected_category': 'order', 'is_positive': True}
    ]
    return pd.DataFrame(test_data)


def classify_with_centroids(classifier, text):
    """
    Klasifikuje text pomocí přímého porovnání s centroidy kategorií.
    
    Args:
        classifier: Instance Classifier s načtenými centroidy
        text (str): Text ke klasifikaci
        
    Returns:
        tuple: (název_nejpodobnější_kategorie, skóre_podobnosti)
    """
    if not text or not isinstance(text, str):
        return None, 0.0
    
    # Kontrola, zda máme k dispozici centroidy
    if not hasattr(classifier, 'category_centroids') or not classifier.category_centroids:
        print("Chyba: Klasifikátor nemá načtené žádné centroidy")
        return None, 0.0
    
    # Zakódování vstupního textu
    text_embedding = classifier.encode([text])[0]
    
    max_similarity = -1
    best_category = None
    
    # Pro každou kategorii spočítáme podobnost s jejím centroidem
    for category, centroid in classifier.category_centroids.items():
        similarity = cosine_similarity(
            text_embedding.reshape(1, -1),
            centroid.reshape(1, -1)
        )[0][0]
        
        if similarity > max_similarity:
            max_similarity = similarity
            best_category = category
    
    return best_category, max_similarity


def compare_classification():
    """Hlavní funkce pro porovnání klasifikátorů."""
    print("🔍 Spouštím porovnání klasifikátorů...")
    
    # Cesta k modelu a centroidům
    model_path = 'mpnet'
    centroids_path = os.path.join(model_path, 'centroids.pkl')
    
    # Kontrola existence modelu a centroidů
    if not os.path.exists(model_path):
        print(f"❌ Chyba: Model nenalezen v cestě: {model_path}")
        return
        
    if not os.path.exists(centroids_path):
        print(f"❌ Chyba: Centroidy nebyly nalezeny v cestě: {centroids_path}")
        return
    
    # Inicializace klasifikátoru
    print(f"✅ Načítám model z: {model_path}")
    classifier = Classifier(model_path=model_path)
    
    # Načtení centroidů
    print(f"✅ Načítám centroidy z: {centroids_path}")
    if not classifier.load_centroids(centroids_path):
        print("❌ Nepodařilo se načíst centroidy")
        return
    
    if not hasattr(classifier, 'category_centroids') or not classifier.category_centroids:
        print("❌ Klasifikátor nemá načtené žádné centroidy")
        return
    
    print(f"✅ Načteno {len(classifier.category_centroids)} kategorií s centroidy")
    print("Dostupné kategorie:", ", ".join(classifier.category_centroids.keys()))
    
    # Nastavení prahu podobnosti
    threshold = 0.79  # Můžete upravit podle potřeby
    
    # Načtení testovacích dat
    test_df = load_test_data()
    
    # Výsledky klasifikace
    results = []
    
    print("\n🔍 Spouštím klasifikaci testovacích dat...")
    print("-" * 80)
    
    # Projít všechny testovací příklady
    for idx, row in test_df.iterrows():
        text = row['text']
        expected = row['expected_category']
        
        # 1. Klasifikace pomocí přímého porovnání s centroidy
        direct_category, direct_score = classify_with_centroids(classifier, text)
        
        # 2. Klasifikace pomocí batch_classify
        df_temp = pd.DataFrame([{'text': text}])
        try:
            df_result = classifier.batch_classify(
                df=df_temp,
                text_column='text',
                class_column='predicted_class',
                similarity_column='similarity',
                threshold=threshold,
                use_numeric_ids=False
            )
            
            batch_category = df_result.iloc[0]['predicted_class'] if not df_result.empty else None
            batch_score = df_result.iloc[0]['similarity'] if not df_result.empty and 'similarity' in df_result.columns else 0.0
        except Exception as e:
            print(f"Chyba při dávkové klasifikaci: {e}")
            batch_category = None
            batch_score = 0.0
        
        # Uložení výsledků
        results.append({
            'text': text,
            'expected': expected,
            'direct_category': direct_category,
            'direct_score': direct_score,
            'batch_category': batch_category,
            'batch_score': batch_score,
            'match': (direct_category == batch_category) or (pd.isna(direct_category) and pd.isna(batch_category))
        })
    
    # Vytvoření DataFrame s výsledky
    results_df = pd.DataFrame(results)
    
    # Přidání informace o shodě s očekávanou kategorií
    results_df['direct_correct'] = results_df.apply(
        lambda x: x['direct_category'] == x['expected'] if pd.notna(x['expected']) else pd.NA, 
        axis=1
    )
    results_df['batch_correct'] = results_df.apply(
        lambda x: x['batch_category'] == x['expected'] if pd.notna(x['expected']) else pd.NA, 
        axis=1
    )
    
    # Výpočet přesnosti
    direct_accuracy = results_df['direct_correct'].mean() * 100
    batch_accuracy = results_df['batch_correct'].mean() * 100
    
    # Výpis výsledků
    print("\n📊 VÝSLEDKY KLASIFIKACE")
    print("=" * 80)
    print(f"Prahová hodnota podobnosti: {threshold}")
    print(f"Počet testovacích příkladů: {len(results_df)}")
    print("-" * 80)
    
    for idx, row in results_df.iterrows():
        print(f"\n📝 Text: {row['text']}")
        print(f"   Očekáváno: {row['expected'] or 'žádná kategorie'}")
        
        # Výpis výsledků přímé klasifikace
        direct_status = "✅" if row['direct_correct'] else "❌" if pd.notna(row['expected']) else "➖"
        print(f"   Metoda 1 (přímá): {row['direct_category'] or 'neurčeno'} (skóre: {row['direct_score']:.3f}) {direct_status}")
        
        # Výpis výsledků dávkové klasifikace
        batch_status = "✅" if row['batch_correct'] else "❌" if pd.notna(row['expected']) else "➖"
        print(f"   Metoda 2 (batch): {row['batch_category'] or 'neurčeno'} (skóre: {row['batch_score']:.3f}) {batch_status}")
        
        # Shoda mezi metodami
        if row['match']:
            print("   🔄 Výsledky se shodují")
        else:
            print("   ⚠️  Rozdílné výsledky!")
    
    # Souhrnné statistiky
    print("\n📊 SOUHRNNÉ STATISTIKY")
    print("=" * 80)
    print(f"Přesnost přímé klasifikace: {direct_accuracy:.1f}%")
    print(f"Přesnost dávkové klasifikace: {batch_accuracy:.1f}%")
    
    match_rate = results_df['match'].mean() * 100
    print(f"Shoda mezi metodami: {match_rate:.1f}%")
    
    # Uložení výsledků do CSV pro další analýzu
    output_file = 'classification_comparison.csv'
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n📄 Výsledky uloženy do: {output_file}")
    
    # Návod k interpretaci výsledků
    print("\n📝 INTERPRETACE VÝSLEDKŮ")
    print("=" * 80)
    print("✅ - Správná klasifikace (shoduje se s očekávanou kategorií)")
    print("❌ - Chybná klasifikace (neshoduje se s očekávanou kategorií)")
    print("➖ - Není určena očekávaná kategorie (negativní příklad)")
    print("🔄 - Obě metody dávají stejný výsledek")
    print("⚠️  - Metody se liší ve výsledku klasifikace")
    
    # Doporučení na základě výsledků
    print("\n💡 DOPORUČENÍ")
    print("=" * 80)
    if match_rate < 80:
        print("🔴 Vysoká míra neshod mezi metodami. Doporučujeme zkontrolovat:")
        print("   - Konzistenci v používání centroidů v obou metodách")
        print("   - Kvalitu a reprezentativnost trénovacích dat")
        print("   - Možné rozdíly v předzpracování textu")
    else:
        print("🟢 Vysoká shoda mezi metodami. Metody pracují konzistentně.")
    
    if direct_accuracy < 70 or batch_accuracy < 70:
        print("\n🔴 Nízká přesnost klasifikace. Zvažte:")
        print("   - Rozšíření trénovacích dat")
        print("   - Úpravu prahové hodnoty (nyní {:.2f})".format(threshold))
        print("   - Kontrolu kvality trénovacích příkladů")


if __name__ == "__main__":
    compare_classification()
