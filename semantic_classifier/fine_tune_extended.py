#!/usr/bin/env python3
"""
Extended fine-tuning script pro semantic classifier.
Implementuje iterativní fine-tuning s automatickým výběrem nejproblematičtějších příkladů.

Proces:
1. Načte trénovací data z Excel souboru
2. Pro každou třídu vypočítá skóre všech pozitiv a negativ vůči kotvě
3. Vybere maximálně 5 nejhorš<PERSON>ch pozitiv a negativ pro vytvoření tripletů
4. Filtruje triplety podle pravidel (pozitiva < 0.95, negativa > 0.4)
5. Provede fine-tuning na vybraných tripletech
6. Opakuje proces dokud existují problematické příklady
"""

import os
import numpy as np
from sentence_transformers import SentenceTransformer, InputExample, losses
from sklearn.metrics.pairwise import cosine_similarity
from torch.utils.data import DataLoader
from tqdm.autonotebook import tqdm

from fine_tune_standalone import (
    detect_device,
    encode_texts,
    save_centroids
)
from semantic_classifier.Classifier import Classifier
from training_data_preparation import load_training_data_from_xlsx

POSITIVE_THRESHOLD = 0.8
NEGATIVE_THRESHOLD = 0.7

def calculate_similarity_scores(model, anchor_text, texts, device):
    """
    Vypočítá kosinusovou podobnost mezi kotvou a seznamem textů.
    
    Args:
        model: SentenceTransformer model
        anchor_text (str): Kotevní text
        texts (list): Seznam textů pro porovnání
        device (str): Zařízení pro výpočet
        
    Returns:
        numpy.ndarray: Pole skóre podobnosti (0..1)
    """
    if not texts:
        return np.array([])
    
    # Zakódujeme kotvu a texty
    anchor_embedding = encode_texts(model, [anchor_text], device)[0]
    text_embeddings = encode_texts(model, texts, device)
    
    # Vypočítáme kosinusovou podobnost
    similarities = cosine_similarity([anchor_embedding], text_embeddings)[0]
    
    # Normalizujeme do rozsahu 0..1 (kosinusová podobnost je -1..1)
    normalized_scores = (similarities + 1) / 2
    
    return normalized_scores


def select_problematic_examples(model, structured_data, device, max_examples_per_class=5):
    """
    Vybere nejproblematičtější pozitiva a negativa pro každou třídu.
    
    Args:
        model: SentenceTransformer model
        structured_data (list): Strukturovaná tréninková data
        device (str): Zařízení pro výpočet
        max_examples_per_class (int): Maximální počet příkladů na třídu
        
    Returns:
        tuple: (selected_triplets, stats) - vybrané triplety a statistiky
    """
    print("🔍 Analyzuji kvalitu trénovacích dat...")
    
    selected_triplets = []
    stats = {
        'total_classes': 0,
        'classes_with_problems': 0,
        'total_positives_analyzed': 0,
        'total_negatives_analyzed': 0,
        'problematic_positives': 0,
        'problematic_negatives': 0,
        'triplets_created': 0
    }
    
    for item in tqdm(structured_data, desc="Analyzuji třídy"):
        category_name = item['category_name']
        anchor_text = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']
        
        stats['total_classes'] += 1
        stats['total_positives_analyzed'] += len(positives)
        stats['total_negatives_analyzed'] += len(negatives)
        
        if not positives or not negatives:
            print(f"⚠️  Třída '{category_name}' nemá dostatek dat, přeskakuji.")
            continue
        
        # Vypočítáme skóre pro pozitiva a negativa
        positive_scores = calculate_similarity_scores(model, anchor_text, positives, device)
        negative_scores = calculate_similarity_scores(model, anchor_text, negatives, device)

        
        # Najdeme problematické příklady
        # Pozitiva: skóre < 0.95 (nejhorší = nejmenší skóre)
        problematic_positive_indices = np.where(positive_scores < POSITIVE_THRESHOLD)[0]
        # Negativa: skóre > 0.4 (nejhorší = největší skóre)  
        problematic_negative_indices = np.where(negative_scores > NEGATIVE_THRESHOLD)[0]
        
        if len(problematic_positive_indices) == 0 and len(problematic_negative_indices) == 0:
            print(f"✅ Třída '{category_name}' nemá problematické příklady.")
            continue
        
        stats['classes_with_problems'] += 1
        
        # Seřadíme problematické příklady podle skóre
        # Pozitiva: od nejmenšího skóre (nejhorší první)
        if len(problematic_positive_indices) > 0:
            sorted_positive_indices = problematic_positive_indices[np.argsort(positive_scores[problematic_positive_indices])]
            selected_positive_indices = sorted_positive_indices[:max_examples_per_class]
        else:
            selected_positive_indices = np.array([], dtype=int)

        # Negativa: od největšího skóre (nejhorší první)
        if len(problematic_negative_indices) > 0:
            sorted_negative_indices = problematic_negative_indices[np.argsort(-negative_scores[problematic_negative_indices])]
            selected_negative_indices = sorted_negative_indices[:max_examples_per_class]
        else:
            selected_negative_indices = np.array([], dtype=int)

        # Sledování původních počtů problematických příkladů
        original_problematic_positives = len(selected_positive_indices)
        original_problematic_negatives = len(selected_negative_indices)

        # Pokud chybí protějšek pro vytvoření tripletů, doplníme nejhorší příklad
        # bez ohledu na threshold
        forced_positives = 0
        forced_negatives = 0

        if len(selected_positive_indices) == 0 and len(selected_negative_indices) > 0:
            # Chybí pozitiva, ale jsou negativa - doplníme nejhorší pozitivum
            if len(positives) > 0:
                worst_positive_idx = np.argmax(positive_scores)
                selected_positive_indices = np.array([worst_positive_idx])
                forced_positives = 1
                print(f"⚠️  Třída '{category_name}': Doplňujem nejhorší pozitivum (skóre: {positive_scores[worst_positive_idx]:.3f}) pro vytvoření tripletů")

        elif len(selected_negative_indices) == 0 and len(selected_positive_indices) > 0:
            # Chybí negativa, ale jsou pozitiva - doplníme nejhorší negativum
            if len(negatives) > 0:
                worst_negative_idx = np.argmin(negative_scores)
                selected_negative_indices = np.array([worst_negative_idx])
                forced_negatives = 1
                print(f"⚠️  Třída '{category_name}': Doplňujem nejhorší negativum (skóre: {negative_scores[worst_negative_idx]:.3f}) pro vytvoření tripletů")

        stats['problematic_positives'] += original_problematic_positives
        stats['problematic_negatives'] += original_problematic_negatives

        # Vytvoříme triplety párováním pozitiv a negativ 1:1 (nyní vždy když máme oboje)
        if len(selected_positive_indices) > 0 and len(selected_negative_indices) > 0:
            # Určíme počet tripletů podle menšího z obou seznamů
            num_triplets = min(len(selected_positive_indices), len(selected_negative_indices))

            for i in range(num_triplets):
                pos_idx = selected_positive_indices[i]
                neg_idx = selected_negative_indices[i]

                positive_text = positives[pos_idx]
                negative_text = negatives[neg_idx]

                # Vytvoříme triplet: [kotva, pozitivum, negativum]
                triplet = InputExample(texts=[anchor_text, positive_text, negative_text])
                selected_triplets.append(triplet)
                stats['triplets_created'] += 1
        
        # Zobrazíme informace pouze pro třídy s problematickými příklady
        if len(selected_positive_indices) > 0 or len(selected_negative_indices) > 0:
            pos_info = ""
            neg_info = ""

            if len(selected_positive_indices) > 0:
                pos_scores = positive_scores[selected_positive_indices]
                pos_info = f"(skóre: {pos_scores.min():.3f}-{pos_scores.max():.3f})"
                if forced_positives > 0:
                    pos_info += f" [+{forced_positives} doplněno]"
            else:
                pos_info = "(žádné)"

            if len(selected_negative_indices) > 0:
                neg_scores = negative_scores[selected_negative_indices]
                neg_info = f"(skóre: {neg_scores.min():.3f}-{neg_scores.max():.3f})"
                if forced_negatives > 0:
                    neg_info += f" [+{forced_negatives} doplněno]"
            else:
                neg_info = "(žádné)"

            # Vypočítáme počet tripletů pro tuto třídu
            triplets_for_class = min(len(selected_positive_indices), len(selected_negative_indices)) if len(selected_positive_indices) > 0 and len(selected_negative_indices) > 0 else 0

            print(f"📊 Třída '{category_name}': "
                  f"{original_problematic_positives} problematických pozitiv {pos_info}, "
                  f"{original_problematic_negatives} problematických negativ {neg_info}, "
                  f"→ {triplets_for_class} tripletů")
    
    return selected_triplets, stats


def has_problematic_examples(model, structured_data, device):
    """
    Zkontroluje, zda stále existují problematické příklady.
    
    Args:
        model: SentenceTransformer model
        structured_data (list): Strukturovaná tréninková data
        device (str): Zařízení pro výpočet
        
    Returns:
        bool: True pokud existují problematické příklady
    """
    print("🔍 Kontroluji, zda stále existují problematické příklady...")
    
    for item in structured_data:
        anchor_text = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']
        
        if not positives or not negatives:
            continue
        
        # Vypočítáme skóre
        positive_scores = calculate_similarity_scores(model, anchor_text, positives, device)
        negative_scores = calculate_similarity_scores(model, anchor_text, negatives, device)
        
        # Zkontrolujeme kritéria
        has_bad_positives = np.any(positive_scores < POSITIVE_THRESHOLD)
        has_bad_negatives = np.any(negative_scores > NEGATIVE_THRESHOLD)
        
        if has_bad_positives or has_bad_negatives:
            return True

    return False


def perform_training_iteration(model, triplets, device, iteration, num_epochs=3, batch_size=16):
    """
    Provede jednu iteraci fine-tuningu na vybraných tripletech.
    
    Args:
        model: SentenceTransformer model
        triplets (list): Seznam InputExample tripletů
        device (str): Zařízení pro výpočet
        iteration (int): Číslo iterace
        num_epochs (int): Počet epoch pro tuto iteraci
        batch_size (int): Velikost batch
        
    Returns:
        SentenceTransformer: Vytrénovaný model
    """
    if not triplets:
        print("⚠️  Žádné triplety pro trénink.")
        return model
    
    print(f"🚀 Iterace {iteration}: Trénuji na {len(triplets)} tripletech...")
    
    # Adjust batch size for MPS to avoid memory issues
    actual_batch_size = batch_size
    if device == 'mps':
        actual_batch_size = min(batch_size, 16)  # Smaller batch for iterative training
        print(f"Using reduced batch size {actual_batch_size} for MPS device (original: {batch_size})")
    
    # Vytvoříme DataLoader
    train_dataloader = DataLoader(
        triplets,
        shuffle=True,
        batch_size=actual_batch_size,
        pin_memory=(device != 'mps')
    )
    
    # Vytvoříme loss funkci
    train_loss = losses.TripletLoss(model=model)
    
    # Nastavení pro model.fit
    fit_params = {
        'train_objectives': [(train_dataloader, train_loss)],
        'epochs': num_epochs,
        'warmup_steps': int(len(train_dataloader) * num_epochs * 0.1),
        'save_best_model': False,  # Pro iterativní trénink neukládáme každou iteraci
        'show_progress_bar': True,
        'optimizer_params': {'lr': 1e-4}
    }
    
    # Spustíme trénink
    model.fit(**fit_params)

    print(f"✅ Iterace {iteration} dokončena.")
    return model


def extended_fine_tune(data_dir, model_dir=None, max_iterations=10,
                      max_examples_per_class=5, epochs_per_iteration=3, batch_size=16):
    """
    Provede rozšířený iterativní fine-tuning s automatickým výběrem problematických příkladů.

    Args:
        model_name (str): Název modelu pro stažení z externích zdrojů
        data_dir (str): Cesta k adresáři s tréninkovými daty
        model_dir (str): Cesta k adresáři pro uložení modelu
        max_iterations (int): Maximální počet iterací
        max_examples_per_class (int): Maximální počet příkladů na třídu v každé iteraci
        epochs_per_iteration (int): Počet epoch v každé iteraci
        batch_size (int): Velikost batch

    Returns:
        tuple: (model, final_stats) - finální model a statistiky
    """
    # Detekce zařízení
    device = detect_device()
    print(f"🖥️  Používám zařízení: {device}")

    # Pokud není zadán model_dir, použijeme výchozí
    if model_dir is None:
        model_dir = "mpnet"

    # Vytvoříme adresář pro model
    os.makedirs(model_dir, exist_ok=True)

    # Načteme nebo vytvoříme model
    if os.path.exists(model_dir) and os.path.exists(os.path.join(model_dir, 'config.json')):
        print(f"📂 Načítám lokální model ze složky: {model_dir}")
        try:
            model = SentenceTransformer(model_dir, device=device)
            print(f"✅ Lokální model úspěšně načten")
        except Exception as e:
            print(f"❌ Chyba při načítání lokálního modelu: {e}")
            print("📥 Stahuji model z externích zdrojů...")
            model = Classifier()
            #model = SentenceTransformer(model_name, device=device)
    else:
        print(f"📥 Stahuji model z externích zdrojů")
        model = Classifier()
        #model = SentenceTransformer(model_name, device=device)

    # Načteme trénovací data
    print("📊 Načítám trénovací data...")
    class_info, structured_data = load_training_data_from_xlsx(data_dir)

    if not structured_data:
        print("❌ Nenalezena žádná platná tréninková data.")
        return model, {}

    print(f"📈 Načteno {len(structured_data)} tříd z trénovacích dat.")

    # Iterativní fine-tuning
    iteration = 0
    total_stats = {
        'iterations_completed': 0,
        'total_triplets_trained': 0,
        'final_problematic_classes': 0
    }

    print(f"🔄 Spouštím iterativní fine-tuning (max {max_iterations} iterací)...")
    print("=" * 60)

    while iteration < max_iterations:
        iteration += 1
        print(f"\n🔄 ITERACE {iteration}/{max_iterations}")
        print("-" * 40)

        # Zkontrolujeme, zda stále existují problematické příklady
        if not has_problematic_examples(model, structured_data, device):
            print("🎉 Všechny příklady splňují kritéria kvality!")
            print("✅ Fine-tuning dokončen předčasně.")
            break

        # Vybereme problematické příklady
        triplets, iteration_stats = select_problematic_examples(
            model, structured_data, device, max_examples_per_class
        )

        if not triplets:
            print("⚠️  Žádné triplety nevyhovují kritériím, ukončuji.")
            break

        print(f"📊 Statistiky iterace {iteration}:")
        for key, value in iteration_stats.items():
            print(f"   {key}: {value}")

        # Provedeme trénink na vybraných tripletech
        model = perform_training_iteration(
            model, triplets, device, iteration,
            num_epochs=epochs_per_iteration, batch_size=batch_size
        )

        # Aktualizujeme celkové statistiky
        total_stats['iterations_completed'] = iteration
        total_stats['total_triplets_trained'] += iteration_stats['triplets_created']

        print(f"✅ Iterace {iteration} dokončena. Trénováno na {len(triplets)} tripletech.")

        # Uložíme model po každé iteraci přímo do model_dir pro uvolnění paměti
        print(f"💾 Ukládám model po iteraci {iteration} do: {model_dir}")
        model.save(model_dir)

        # Znovu načteme model pro další iteraci (pomůže s uvolněním paměti)
        print(f"🔄 Znovu načítám model pro další iteraci...")
        model = SentenceTransformer(model_dir, device=device)

    # Finální kontrola a uložení
    print("\n" + "=" * 60)
    print("🏁 FINÁLNÍ FÁZE")
    print("-" * 40)

    # Finální kontrola problematických příkladů
    final_problems = has_problematic_examples(model, structured_data, device)
    if final_problems:
        print("⚠️  Stále existují některé problematické příklady.")
        total_stats['final_problematic_classes'] = sum(1 for item in structured_data
                                                      if has_class_problems(model, item, device))
    else:
        print("🎉 Všechny příklady nyní splňují kritéria kvality!")
        total_stats['final_problematic_classes'] = 0

    # Finální model je už uložen v model_dir po poslední iteraci
    if total_stats['iterations_completed'] > 0:
        print(f"✅ Finální model je už uložen v: {model_dir}")
        # Model je už načtený z poslední iterace, můžeme ho použít pro centroidy
    else:
        # Žádné iterace neproběhly, uložíme původní model
        print(f"💾 Ukládám původní model (žádné iterace neproběhly) do: {model_dir}")
        model.save(model_dir)

    # Vypočítáme a uložíme centroidy
    print("🎯 Počítám finální centroidy...")
    from fine_tune_standalone import calculate_category_centroids
    category_centroids = calculate_category_centroids(model, structured_data, device)

    if category_centroids:
        centroids_path = os.path.join(model_dir, 'centroids.pkl')
        save_centroids(category_centroids, class_info, centroids_path)
        print(f"💾 Centroidy uloženy do: {centroids_path}")

    # Model je uložen přímo v model_dir, žádné další čištění není potřeba
    print(f"✅ Model a centroidy jsou připraveny v: {model_dir}")

    # Finální statistiky
    print(f"\n📊 FINÁLNÍ STATISTIKY:")
    print(f"   Dokončeno iterací: {total_stats['iterations_completed']}")
    print(f"   Celkem tripletů trénováno: {total_stats['total_triplets_trained']}")
    print(f"   Problematické třídy na konci: {total_stats['final_problematic_classes']}")

    return model, total_stats


def has_class_problems(model, class_item, device):
    """Pomocná funkce pro kontrolu problémů v jedné třídě."""
    anchor_text = class_item['anchor_triplet']
    positives = class_item['positives_variants']
    negatives = class_item['negatives']

    if not positives or not negatives:
        return False

    positive_scores = calculate_similarity_scores(model, anchor_text, positives, device)
    negative_scores = calculate_similarity_scores(model, anchor_text, negatives, device)

    has_bad_positives = np.any(positive_scores < POSITIVE_THRESHOLD)
    has_bad_negatives = np.any(negative_scores > NEGATIVE_THRESHOLD)

    return has_bad_positives or has_bad_negatives


if __name__ == "__main__":
    """
    Hlavní sekce pro spuštění extended fine-tuning.
    """
    # Nastavení prostředí
    os.environ["TOKENIZERS_PARALLELISM"] = "false"

    # Parametry extended fine-tuningu
    #model_name = 'sentence-transformers/paraphrase-multilingual-mpnet-base-v2'
    data_dir = 'training_data'
    model_dir = 'mpnet'

    print("🚀 Spouštím Extended Fine-tuning...")
    print("=" * 60)
    print("📋 Parametry:")
    #print(f"   Model: {model_name}")
    print(f"   Data: {data_dir}")
    print(f"   Výstup: {model_dir}")
    print(f"   Max iterací: 10")
    print(f"   Max příkladů na třídu: 5")
    print(f"   Epoch na iteraci: 3")
    print("=" * 60)

    try:
        # Spustíme extended fine-tuning
        final_model, stats = extended_fine_tune(
            data_dir=data_dir,
            model_dir=model_dir,
            max_iterations=5,
            max_examples_per_class=5,
            epochs_per_iteration=3,
            batch_size=8,

        )

        print("\n" + "=" * 60)
        print("🎉 EXTENDED FINE-TUNING DOKONČEN!")
        print("=" * 60)
        print(f"✅ Model uložen v: {model_dir}")
        print(f"📊 Celkové statistiky:")
        for key, value in stats.items():
            print(f"   {key}: {value}")

        # Doporučení pro další kroky
        if stats.get('final_problematic_classes', 0) > 0:
            print(f"\n💡 DOPORUČENÍ:")
            print(f"   Stále existuje {stats['final_problematic_classes']} problematických tříd.")
            print(f"   Zvažte:")
            print(f"   - Zvýšení max_iterations")
            print(f"   - Přidání více epoch na iteraci")
            print(f"   - Kontrolu kvality trénovacích dat")
        else:
            print(f"\n🎯 PERFEKTNÍ VÝSLEDEK!")
            print(f"   Všechny třídy nyní splňují kritéria kvality.")
            print(f"   Model je připraven k použití.")

    except Exception as e:
        print(f"\n❌ CHYBA při extended fine-tuning:")
        print(f"   {str(e)}")
        print(f"\n🔧 Možná řešení:")
        print(f"   - Zkontrolujte trénovací data v {data_dir}")
        print(f"   - Ověřte dostupnost GPU/MPS")
        print(f"   - Snižte batch_size nebo max_examples_per_class")
        raise
