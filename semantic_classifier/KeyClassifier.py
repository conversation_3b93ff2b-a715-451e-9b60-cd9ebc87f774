import os
import numpy as np
import torch
from sentence_transformers import SentenceTransformer
from typing import Dict, List, Tuple

model_name = "sentence-transformers/all-mpnet-base-v2"
model_dir = "./semantic_classifier/mpnet"

class KeyClassifier:
    def __init__(self, model_name: str = model_name, model_dir: str = model_dir, device: str = "cpu"):
        self.model_name = model_name
        self.model_dir = model_dir
        self.device = device
        self.load()
    
    def _is_valid_model_dir(self, path: str) -> bool:
        # Kontrola existence základních souborů modelu
        if not os.path.isdir(path):
            return False
        files = os.listdir(path)
        # Minimálně config.json a pytorch_model.bin nebo model.safetensors
        return (
            ("config.json" in files) and ("pytorch_model.bin" in files or "model.safetensors" in files)
        )

    def load(self, use_local: bool = True):
        """Načte model z HuggingFace nebo z lokální složky. Pokud není validní model, st<PERSON><PERSON>e jej."""
        # Pokud je v model_dir validní model, pou<PERSON><PERSON><PERSON><PERSON> jej
        if use_local and self._is_valid_model_dir(self.model_dir):
            path = self.model_dir
        else:
            # Stáhneme model z HuggingFace a uložíme do model_dir
            print(f"Stahuji model z HuggingFace: {self.model_name} do {self.model_dir}")
            model = SentenceTransformer(self.model_name, device=self.device)
            os.makedirs(self.model_dir, exist_ok=True)
            model.save(self.model_dir)
            path = self.model_dir
        self.model = SentenceTransformer(path, device=self.device)
        # Zjistit rozměr embeddingů
        test_emb = self.model.encode(["test"], convert_to_tensor=False)
        self.embedding_dim = len(test_emb[0])

        # Po načtení modelu zkusíme načíst i prototypy, pokud existují
        prototypes_path = os.path.join(self.model_dir, "prototypes.bin")
        if os.path.isfile(prototypes_path):
            try:
                self.load_prototypes_binary(prototypes_path)
                print(f"Prototypy načteny z: {prototypes_path} (počet: {len(self.class_prototypes)})")
            except Exception as e:
                print(f"Chyba při načítání prototypů: {e}")

    def encode(self, texts) -> np.ndarray:
        """Vrátí numpy array embeddings - kompatibilní s C++."""
        if self.model is None:
            raise RuntimeError("Model není načten.")
        
        if isinstance(texts, str):
            texts = [texts]
        
        # convert_to_tensor=False vrací numpy arrays
        embeddings = self.model.encode(texts, convert_to_tensor=False, normalize_embeddings=True)
        return np.array(embeddings, dtype=np.float32)
    
    def save(self):
        """Uloží model do složky."""
        if self.model is None:
            raise RuntimeError("Model není načten.")
        os.makedirs(self.model_dir, exist_ok=True)
        self.model.save(self.model_dir)
    
    def set_class_prototypes(self, class_texts: Dict[str, List[str]]):
        """
        Nastaví prototypy tříd z příkladů.
        Uloží jako normalized numpy arrays.
        """
        if self.model is None:
            raise RuntimeError("Model není načten.")
        
        if not class_texts:
            raise ValueError("class_texts nesmí být prázdný")
        
        self.class_prototypes = {}
        self.class_names = []
        self.class_ids = []  # seznam id (indexů)
        self.class_id_to_name = {}  # mapování id -> jméno
        self.class_name_to_id = {}  # mapování jméno -> id

        # Podpora dvou vstupních formátů:
        # 1) Dict[str, List[str]]  -> {class_name: [texts...]}
        # 2) List[dict] (výstup z load_training_data_from_xlsx) ->
        #    [{'category_name': ..., 'anchor_triplet': ..., 'positives_variants': [...], 'negatives': [...]}, ...]
        if isinstance(class_texts, dict):
            items_iter = class_texts.items()
        elif isinstance(class_texts, list):
            mapping = {}
            for entry in class_texts:
                if not isinstance(entry, dict):
                    continue
                class_name = entry.get('category_name') or entry.get('class_name')
                if not class_name:
                    # přeskočíme položky bez jména
                    continue
                texts = []
                anchor = entry.get('anchor_triplet')
                if anchor:
                    texts.append(anchor)
                positives = entry.get('positives_variants') or entry.get('positives') or []
                if positives:
                    texts.extend(positives)
                # Pokud nejsou žádné texty, přeskočíme
                if not texts:
                    continue
                mapping[class_name] = texts
            items_iter = mapping.items()
        else:
            raise ValueError("Unsupported class_texts type. Expect dict or list of dicts.")

        for class_name, texts in items_iter:
            if not texts:
                raise ValueError(f"Třída '{class_name}' nemá žádné příklady")

            embeddings = self.encode(texts)  # Already numpy array
            centroid = np.mean(embeddings, axis=0).astype(np.float32)
            # Bezpečná normalizace (zabrání dělení nulou)
            norm = np.linalg.norm(centroid)
            if norm == 0 or np.isnan(norm):
                # necháme centroid nezměněný, ale logicky by to nemělo nastat
                normalized = centroid
            else:
                normalized = centroid / norm

            self.class_prototypes[class_name] = normalized
            self.class_names.append(class_name)
            class_id = len(self.class_names) - 1
            self.class_ids.append(class_id)
            self.class_id_to_name[class_id] = class_name
            self.class_name_to_id[class_name] = class_id

    def cosine_similarity(self, query_emb: np.ndarray) -> Tuple[np.ndarray, List[int]]:
        # Vždy 2D vstup
        if query_emb.ndim == 1:
            query_emb = query_emb[np.newaxis, :]

        # Normalizace dotazů
        norms = np.linalg.norm(query_emb, axis=1, keepdims=True)
        norms[norms == 0] = 1
        query_normalized = query_emb / norms

        # Kosínová podobnost: (n_texts, emb_dim) @ (emb_dim, n_classes)
        scores = np.dot(query_normalized, self.proto_matrix.T)  # (n_texts, n_classes)
        return scores, self.class_ids
    def _cosine_similarity_vectorized(self, query_emb: np.ndarray) -> Tuple[np.ndarray, List[int]]:
        """
        Vektorizovaný výpočet cosine similarity.
        Připraveno pro přímý port do C++ s BLAS.
        query_emb: (embedding_dim,) nebo (n_texts, embedding_dim)
        Vrací:
            - scores: (n_classes,) nebo (n_texts, n_classes)
            - class_ids: List[int]
        """
        if not self.class_prototypes:
            raise RuntimeError("Class prototypes nejsou nastaveny.")
        proto_matrix = np.stack([self.class_prototypes[name] for name in self.class_names])  # (n_classes, emb_dim)
        # Pokud je query_emb 1D, rozšíříme na 2D
        if query_emb.ndim == 1:
            query_emb = query_emb[np.newaxis, :]
        # Normalizace dotazů
        query_norm = np.linalg.norm(query_emb, axis=1, keepdims=True)
        query_norm[query_norm == 0] = 1  # ochrana proti dělení nulou
        query_normalized = query_emb / query_norm
        # Kosinová podobnost: (n_texts, emb_dim) x (emb_dim, n_classes) -> (n_texts, n_classes)
        scores = np.dot(query_normalized, proto_matrix.T)
        if scores.shape[0] == 1:
            scores = scores[0]  # pro zpětnou kompatibilitu
        return scores, self.class_ids

    def classify(self, text: str) -> Tuple[int, float]:
        """
        Klasifikuje jeden text.
        Vrací (class_id, score).
        """
        query_emb = self.encode([text])[0]  # Get first (and only) embedding
        scores, class_ids = self._cosine_similarity_vectorized(query_emb)

        best_idx = np.argmax(scores)
        return class_ids[best_idx], float(scores[best_idx])

    def classify_batch(self, texts: List[str]) -> List[Tuple[int, float]]:
        """
        Plně vektorizovaná batch klasifikace.
        Vrací seznam (class_id, score).
        """
        if not texts:
            return []
        query_embs = self.encode(texts)  # (n_texts, emb_dim)
        scores, class_ids = self._cosine_similarity_vectorized(query_embs)  # scores: (n_texts, n_classes)
        best_idx = np.argmax(scores, axis=1)  # (n_texts,)
        best_scores = scores[np.arange(scores.shape[0]), best_idx]
        return [(class_ids[i], float(best_scores[i])) for i in best_idx]

    def classify_top_k(self, text: str, k: int = 3, threshold: float = 0.0) -> List[Tuple[int, float]]:
        """
        Vrací top-k predikcí s confidence > threshold jako (class_id, score).
        """
        query_emb = self.encode([text])[0]
        scores, class_ids = self._cosine_similarity_vectorized(query_emb)

        # Vytvoř páry (score, class_id) a seřaď
        scored_classes = [(float(score), cid) for score, cid in zip(scores, class_ids) if score >= threshold]
        scored_classes.sort(reverse=True)
        
        return [(cid, score) for score, cid in scored_classes[:k]]

    def save_prototypes_binary(self, filepath: str):
        """
        Uloží prototypy v binárním formátu kompatibilním s C++.
        Format: [num_classes][embedding_dim][class1_name_len][class1_name][class1_embedding]...
        """
        if not self.class_prototypes:
            raise RuntimeError("Žádné prototypy k uložení")
        
        with open(filepath, 'wb') as f:
            # Header
            f.write(len(self.class_names).to_bytes(4, 'little'))  # num_classes
            f.write(self.embedding_dim.to_bytes(4, 'little'))     # embedding_dim
            
            # Data pro každou třídu
            for class_name in self.class_names:
                name_bytes = class_name.encode('utf-8')
                f.write(len(name_bytes).to_bytes(4, 'little'))    # name length
                f.write(name_bytes)                               # name
                prototype = self.class_prototypes[class_name]
                f.write(prototype.tobytes())                      # embedding (float32)
    
    def load_prototypes_binary(self, filepath: str):
        """Načte prototypy z binárního souboru."""
        with open(filepath, 'rb') as f:
            num_classes = int.from_bytes(f.read(4), 'little')
            embedding_dim = int.from_bytes(f.read(4), 'little')
            self.embedding_dim = embedding_dim
            self.class_prototypes = {}
            self.class_names = []
            for _ in range(num_classes):
                name_len = int.from_bytes(f.read(4), 'little')
                class_name = f.read(name_len).decode('utf-8')
                embedding_bytes = f.read(embedding_dim * 4)  # 4 bytes per float32
                embedding = np.frombuffer(embedding_bytes, dtype=np.float32)
                self.class_names.append(class_name)
                self.class_prototypes[class_name] = embedding
        # --- DOPLNĚNÍ MAPOVÁNÍ ID ---
        self.class_ids = list(range(len(self.class_names)))
        self.class_id_to_name = {i: name for i, name in enumerate(self.class_names)}
        self.class_name_to_id = {name: i for i, name in enumerate(self.class_names)}
        print(f"[DEBUG] load_prototypes_binary: id(self)={id(self)}, načteno prototypů: {len(self.class_prototypes)}")

    def get_prototype_matrix(self) -> np.ndarray:
        """
        Vrátí matici prototypů pro export do C++.
        Shape: (num_classes, embedding_dim)
        """
        if not self.class_prototypes:
            return np.array([])
        
        return np.stack([self.class_prototypes[name] for name in self.class_names])
    
    def export_for_cpp(self, base_path: str):
        """
        Exportuje model pro C++ - pouze enkódování textů do vektorů.
        Prototypy exportuje jako raw float arrays.
        """
        os.makedirs(base_path, exist_ok=True)
        
        # 1. Export prototypů jako raw binary (pro C++ memcpy)
        self._export_prototypes_raw(os.path.join(base_path, "prototypes.bin"))
        
        # 2. Export ONNX modelu pro text encoding
        try:
            self._export_text_encoder_onnx(base_path)
            print("✅ ONNX model exportován")
        except Exception as e:
            print(f"❌ ONNX export selhal: {e}")
            print("Zkuste alternativní metody (TorchScript, Hugging Face Optimum)")
            
        # 3. Export tokenizer files pro C++
        self._export_tokenizer_files(base_path)
        
        # 4. C++ header s metadata
        self._generate_cpp_header(os.path.join(base_path, "model_config.h"))
        
        print(f"\n📁 Exportováno do: {base_path}")
        print("📝 Pro C++: načíst ONNX model + prototypes.bin")
    
    def _export_prototypes_raw(self, filepath: str):
        """
        Export prototypů jako raw float32 array pro rychlé načtení v C++.
        Format: [float32 matrix: num_classes × embedding_dim]
        """
        if not self.class_prototypes:
            raise RuntimeError("Žádné prototypy k exportu")
        
        # Vytvoř matici prototypů
        proto_matrix = self.get_prototype_matrix()  # Shape: (num_classes, embedding_dim)
        
        # Uložit jako raw binary float32
        proto_matrix.astype(np.float32).tofile(filepath)
        
        # Uložit rozměry vedle
        with open(filepath.replace('.bin', '_dims.txt'), 'w') as f:
            f.write(f"{len(self.class_names)} {self.embedding_dim}")
        
        print(f"Prototypy: {len(self.class_names)} tříd × {self.embedding_dim} dimenzí")
    
    def _export_text_encoder_onnx(self, base_path: str):
        """
        Exportuje pouze text encoding část sentence transformeru.
        Optimalizováno pro text → embedding inference v C++.
        """
        if self.model is None:
            raise RuntimeError("Model není načten")
        
        # Získej underlying transformer model
        transformer_model = self.model[0].auto_model  # První modul
        tokenizer = self.model.tokenizer
        
        # Dummy input pro ONNX tracing
        sample_text = "Hello world"
        inputs = tokenizer(sample_text, return_tensors="pt", padding=True, truncation=True)
        
        # Nastavit model do eval módu
        transformer_model.eval()
        
        # ONNX export
        onnx_path = os.path.join(base_path, "text_encoder.onnx")
        
        with torch.no_grad():
            torch.onnx.export(
                transformer_model,
                (inputs['input_ids'], inputs['attention_mask']),
                onnx_path,
                input_names=['input_ids', 'attention_mask'],
                output_names=['embeddings'],
                dynamic_axes={
                    'input_ids': {0: 'batch_size', 1: 'sequence_length'},
                    'attention_mask': {0: 'batch_size', 1: 'sequence_length'},
                    'embeddings': {0: 'batch_size'}
                },
                opset_version=14,  # Novější verze pro lepší kompatibilitu
                do_constant_folding=True
            )
    
    def _export_tokenizer_files(self, base_path: str):
        """
        Exportuje tokenizer files pro C++ (vocab, merges, config).
        """
        if self.model is None:
            return
            
        tokenizer = self.model.tokenizer
        tokenizer_path = os.path.join(base_path, "tokenizer")
        
        # Uložit tokenizer files
        tokenizer.save_pretrained(tokenizer_path)
        
        # Dodatečné info pro C++
        tokenizer_info = {
            "model_max_length": tokenizer.model_max_length,
            "vocab_size": len(tokenizer),
            "pad_token_id": tokenizer.pad_token_id,
            "cls_token_id": tokenizer.cls_token_id if hasattr(tokenizer, 'cls_token_id') else None,
            "sep_token_id": tokenizer.sep_token_id if hasattr(tokenizer, 'sep_token_id') else None,
        }
        
        import json
        with open(os.path.join(base_path, "tokenizer_config.json"), 'w') as f:
            json.dump(tokenizer_info, f, indent=2)
    
    def _generate_cpp_header(self, header_path: str):
        """
        Generuje C++ header s konstantami a strukturami.
        """
        header_content = f"""#ifndef MODEL_CONFIG_H
#define MODEL_CONFIG_H

// Auto-generated model configuration
namespace ModelConfig {{
    constexpr int EMBEDDING_DIM = {self.embedding_dim};
    constexpr int NUM_CLASSES = {len(self.class_names)};
    constexpr int MAX_SEQUENCE_LENGTH = 512;  // Adjust based on your model
    
    // Class names (ordered as in prototypes matrix)
    const char* CLASS_NAMES[NUM_CLASSES] = {{
        {', '.join([f'"{name}"' for name in self.class_names])}
    }};
    
    // File paths
    constexpr const char* ONNX_MODEL_PATH = "text_encoder.onnx";
    constexpr const char* PROTOTYPES_PATH = "prototypes.bin";
    constexpr const char* TOKENIZER_PATH = "tokenizer";
}}

// Suggested C++ structure
struct ClassificationResult {{
    int class_id;
    const char* class_name;
    float confidence;
}};

#endif // MODEL_CONFIG_H"""
        
        with open(header_path, 'w') as f:
            f.write(header_content)
    
    def _export_sentence_transformer_onnx(self, onnx_path: str):
        """
        Exportuje sentence transformer do ONNX formátu.
        Pozor: Ne všechny sentence-transformers modely podporují ONNX export!
        """
        if self.model is None:
            raise RuntimeError("Model není načten.")
        try:
            import torch.onnx
            dummy_input = (torch.randint(0, 1000, (1, 128), dtype=torch.long),)
            underlying_model = self.model[0].auto_model
            underlying_model.eval()
            torch.onnx.export(
                underlying_model,
                dummy_input,
                onnx_path,
                input_names=['input_ids'],
                output_names=['embeddings'],
                dynamic_axes={
                    'input_ids': {0: 'batch_size', 1: 'sequence_length'},
                    'embeddings': {0: 'batch_size'}
                },
                opset_version=11
            )
        except Exception as e:
            try:
                from optimum.onnxruntime import ORTModelForFeatureExtraction
                ort_model = ORTModelForFeatureExtraction.from_pretrained(
                    self.model_name, 
                    export=True
                )
                ort_model.save_pretrained(os.path.dirname(onnx_path))
            except ImportError:
                raise RuntimeError("ONNX export vyžaduje 'optimum' nebo manuální implementaci")
    
    def _export_tokenizer_info(self, tokenizer_path: str):
        """
        Exportuje informace o tokenizeru (vocab, speciální tokeny).
        Pro kompletní C++ implementaci.
        """
        if self.model is None:
            return
        
        try:
            tokenizer = self.model.tokenizer
            tokenizer_info = {
                "vocab_size": tokenizer.vocab_size,
                "model_max_length": tokenizer.model_max_length,
                "special_tokens": {
                    "cls_token": getattr(tokenizer, 'cls_token', None),
                    "sep_token": getattr(tokenizer, 'sep_token', None),
                    "pad_token": getattr(tokenizer, 'pad_token', None),
                    "unk_token": getattr(tokenizer, 'unk_token', None)
                }
            }
            
            import json
            with open(tokenizer_path, 'w') as f:
                json.dump(tokenizer_info, f, indent=2)
                
        except Exception as e:
            print(f"Warning: Tokenizer info export failed: {e}")