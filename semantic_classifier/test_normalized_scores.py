#!/usr/bin/env python3
"""
Test normalizovaných skóre podobnosti v SBertClassifier.
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že všechny metody vracejí skóre v rozsahu 0-1.
"""

import os
import sys
import numpy as np
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_classifier.SBertClassifier import SBertClassifier

def test_normalized_scores():
    """Test normalizovaných skóre podobnosti."""
    print("=== Test normalizovaných skóre podobnosti ===")
    
    # 1. Inicializace
    classifier = SBertClassifier()
    
    # 2. Vytvoření prototypů
    class_texts = {
        "Pozitivní": ["skvělý", "výborný", "fantastický", "úžasný"],
        "Negativní": ["špatný", "hrozn<PERSON>", "ot<PERSON><PERSON><PERSON><PERSON>", "děsivý"],
        "Neutrální": ["normální", "průměrn<PERSON>", "obyčejný", "standardní"]
    }
    
    classifier.create_and_save_prototypes(class_texts)
    
    # 3. Test metody similarity()
    print("\n--- Test metody similarity() ---")
    test_pairs = [
        ("skvělý", "výborný"),      # vysoká podobnost
        ("skvělý", "špatný"),       # nízká podobnost  
        ("normální", "průměrný"),   # střední podobnost
        ("úplně", "jiný"),          # velmi nízká podobnost
        ("identický", "identický")  # identické texty
    ]
    
    similarity_scores = []
    for text1, text2 in test_pairs:
        score = classifier.similarity(text1, text2)
        similarity_scores.append(score)
        print(f"'{text1}' vs '{text2}': {score:.3f}")
        
        # Ověření rozsahu
        if not (0.0 <= score <= 1.0):
            print(f"❌ CHYBA: Skóre {score:.3f} není v rozsahu [0, 1]!")
        else:
            print(f"✅ Skóre v rozsahu [0, 1]")
    
    print(f"\nRozsah similarity skóre: {min(similarity_scores):.3f} - {max(similarity_scores):.3f}")
    
    # 4. Test metody classify()
    print("\n--- Test metody classify() ---")
    test_texts = [
        "úžasný výsledek",      # mělo by být Pozitivní
        "hrozná situace",       # mělo by být Negativní
        "běžný den",            # mělo by být Neutrální
        "xyz abc 123",          # neznámý text
        "super fantastický"     # silně pozitivní
    ]
    
    classify_scores = []
    for text in test_texts:
        class_id, confidence = classifier.classify(text)
        class_name = classifier.get_class_name(class_id)
        classify_scores.append(confidence)
        
        print(f"'{text}' -> {class_name} (confidence: {confidence:.3f})")
        
        # Ověření rozsahu
        if not (0.0 <= confidence <= 1.0):
            print(f"❌ CHYBA: Confidence {confidence:.3f} není v rozsahu [0, 1]!")
        else:
            print(f"✅ Confidence v rozsahu [0, 1]")
    
    print(f"\nRozsah classify confidence: {min(classify_scores):.3f} - {max(classify_scores):.3f}")
    
    # 5. Test metody classify_batch()
    print("\n--- Test metody classify_batch() ---")
    batch_results = classifier.classify_batch(test_texts)
    
    batch_scores = []
    print("Batch výsledky:")
    for i, (text, (class_id, confidence)) in enumerate(zip(test_texts, batch_results)):
        class_name = classifier.get_class_name(class_id)
        batch_scores.append(confidence)
        
        print(f"  {i+1}. '{text}' -> {class_name} (confidence: {confidence:.3f})")
        
        # Ověření rozsahu
        if not (0.0 <= confidence <= 1.0):
            print(f"❌ CHYBA: Batch confidence {confidence:.3f} není v rozsahu [0, 1]!")
        else:
            print(f"✅ Batch confidence v rozsahu [0, 1]")
    
    print(f"\nRozsah batch confidence: {min(batch_scores):.3f} - {max(batch_scores):.3f}")
    
    # 6. Porovnání jednotlivé vs batch klasifikace
    print("\n--- Porovnání jednotlivé vs batch klasifikace ---")
    individual_results = []
    for text in test_texts:
        class_id, confidence = classifier.classify(text)
        individual_results.append((class_id, confidence))
    
    print("Porovnání výsledků:")
    all_match = True
    for i, (text, individual, batch) in enumerate(zip(test_texts, individual_results, batch_results)):
        ind_class, ind_conf = individual
        bat_class, bat_conf = batch
        
        class_match = ind_class == bat_class
        conf_match = abs(ind_conf - bat_conf) < 1e-6
        
        status = "✅" if (class_match and conf_match) else "❌"
        print(f"  {i+1}. {status} '{text}'")
        print(f"     Individual: class={ind_class}, conf={ind_conf:.6f}")
        print(f"     Batch:      class={bat_class}, conf={bat_conf:.6f}")
        
        if not (class_match and conf_match):
            all_match = False
    
    if all_match:
        print("✅ Všechny výsledky se shodují!")
    else:
        print("❌ Některé výsledky se neshodují!")
    
    # 7. Test extrémních případů
    print("\n--- Test extrémních případů ---")
    extreme_tests = [
        "",                     # prázdný text
        "a",                    # velmi krátký text
        " ".join(["slovo"] * 100),  # velmi dlouhý text
        "123 456 789",          # pouze čísla
        "!@#$%^&*()",          # pouze speciální znaky
    ]
    
    for text in extreme_tests:
        try:
            if text == "":
                print(f"Prázdný text: přeskakuji")
                continue
                
            class_id, confidence = classifier.classify(text)
            class_name = classifier.get_class_name(class_id)
            
            display_text = text if len(text) <= 20 else text[:20] + "..."
            print(f"'{display_text}' -> {class_name} (confidence: {confidence:.3f})")
            
            if not (0.0 <= confidence <= 1.0):
                print(f"❌ CHYBA: Extrémní confidence {confidence:.3f} není v rozsahu [0, 1]!")
            else:
                print(f"✅ Extrémní confidence v rozsahu [0, 1]")
                
        except Exception as e:
            print(f"❌ Chyba při zpracování '{text[:20]}...': {e}")
    
    # 8. Statistiky
    print("\n--- Celkové statistiky ---")
    all_scores = similarity_scores + classify_scores + batch_scores
    
    print(f"Celkem testovaných skóre: {len(all_scores)}")
    print(f"Minimální skóre: {min(all_scores):.6f}")
    print(f"Maximální skóre: {max(all_scores):.6f}")
    print(f"Průměrné skóre: {np.mean(all_scores):.6f}")
    print(f"Směrodatná odchylka: {np.std(all_scores):.6f}")
    
    # Ověření, že všechna skóre jsou v rozsahu [0, 1]
    out_of_range = [s for s in all_scores if not (0.0 <= s <= 1.0)]
    if out_of_range:
        print(f"❌ {len(out_of_range)} skóre mimo rozsah [0, 1]: {out_of_range}")
    else:
        print("✅ Všechna skóre jsou v rozsahu [0, 1]")
    
    print("\n✅ Test normalizovaných skóre dokončen!")

if __name__ == "__main__":
    try:
        test_normalized_scores()
    except Exception as e:
        print(f"❌ Chyba během testování: {e}")
        import traceback
        traceback.print_exc()
