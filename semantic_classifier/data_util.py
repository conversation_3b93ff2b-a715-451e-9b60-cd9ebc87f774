import pandas as pd
from semantic_classifier.training_data_preparation import load_training_data_from_xlsx

def generate_triplet_dataset(xlsx_path):
    class_info, training_data = load_training_data_from_xlsx(xlsx_path)
    triplets = []

    for item in training_data:
        anchor = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']

        n_pos = len(positives)
        n_neg = len(negatives)
        max_count = max(n_pos, n_neg)

        # Vytvoříme všechny kombinace každý s každým
        pairs = [(p, n) for p in positives for n in negatives]

        if max_count < 5:
            selected_pairs = pairs
        elif max_count <= 15:
            selected_pairs = pairs[:100]
        else:
            selected_pairs = pairs[:200]

        for positive, negative in selected_pairs:
            triplets.append({
                'anchor': anchor,
                'positive': positive,
                'negative': negative
            })

    df = pd.DataFrame(triplets)
    return df

# Použití:
# df = generate_triplet_dataset('cesta/k/training_set.xlsx')
# df.to_csv('triplet_dataset.csv', index=False)