"""
Kontrolní skript pro testování kvality klasifikace.

Načte tréninková data z Excel souboru a otestuje klasifikaci
pozitivních a negativních příkladů proti kotvě každé kategorie.
Výsledky zobrazí v přehledné formě pro analýzu kvality dat.
"""

import os
import sys
from KeyClassifier import KeyClassifier
from training_data_preparation import load_training_data_from_xlsx
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np


def test_classification_quality(data_path):
    """
    Otestuje kvalitu klasifikace na trénovacích datech.
    
    Args:
        data_path (str): Cesta k Excel souboru s tréninkovými daty
    """
    print("=" * 80)
    print("KONTROLA KVALITY KLASIFIKACE")
    print("=" * 80)
    print()
    
    # Načtení dat
    print(f"Načítám data z: {data_path}")
    class_info, structured_data = load_training_data_from_xlsx(data_path)
    
    if not structured_data:
        print("❌ Nepodařilo se načíst žádná data!")
        return
    
    print(f"✅ Načteno {len(structured_data)} kategorií")
    print()
    
    # Inicializace klasifikátoru
    print(f"Inicializuji klasifikátor")
    classifier = KeyClassifier()

    # Vypočítáme centroidy pro srovnání
    print("Počítám centroidy kategorií pro srovnání...")
    classifier.set_class_prototypes(structured_data)
    has_centroids = classifier.class_prototypes is not None
    if has_centroids:
        print(f"✅ Centroidy vypočítány pro {len(classifier.class_prototypes)} kategorií")
    else:
        print("⚠️ Centroidy se nepodařilo vypočítat")
    print()
    
    # Analýza každé kategorie
    overall_stats = {
        'categories': 0,
        'positive_correct': 0,
        'positive_total': 0,
        'negative_correct': 0,
        'negative_total': 0
    }
    
    for category_data in structured_data:
        category_name = category_data['category_name']
        anchor = category_data['anchor_triplet']
        positives = category_data['positives_variants']
        negatives = category_data['negatives']
        
        print(f"📂 KATEGORIE: {category_name}")
        print(f"🎯 KOTVA: '{anchor}'")
        print(f"✅ Pozitivní příklady: {len(positives)}")
        print(f"❌ Negativní příklady: {len(negatives)}")
        print("-" * 60)
        
        # Zakódujeme kotvu
        anchor_embedding = classifier.encode([anchor])[0]

        # Získáme centroid kategorie pro srovnání
        category_centroid = None
        if has_centroids and category_name in classifier.class_prototypes:
            category_centroid = classifier.class_prototypes[category_name]
        
        # Test pozitivních příkladů
        print("🔍 TESTOVÁNÍ POZITIVNÍCH PŘÍKLADŮ:")
        positive_reliabilities = []
        positive_centroid_reliabilities = []

        for i, positive in enumerate(positives, 1):
            positive_embedding = classifier.encode([positive])[0]

            # Spolehlivost proti kotvě
            anchor_similarity = cosine_similarity(
                anchor_embedding.reshape(1, -1),
                positive_embedding.reshape(1, -1)
            )[0][0]
            anchor_reliability = (anchor_similarity + 1) / 2
            #anchor_reliability = max(0.0, anchor_similarity)
            positive_reliabilities.append(anchor_reliability)

            # Spolehlivost proti centroidu (pokud existuje)
            centroid_reliability = None
            if category_centroid is not None:
                centroid_similarity = cosine_similarity(
                    category_centroid.reshape(1, -1),
                    positive_embedding.reshape(1, -1)
                )[0][0]
                #centroid_reliability = max(0.0, centroid_similarity)
                centroid_reliability = (centroid_similarity + 1) / 2
                positive_centroid_reliabilities.append(centroid_reliability)

            status = "✅" if anchor_reliability > 0.8 else "⚠️" if anchor_reliability > 0.7 else "❌"

            if centroid_reliability is not None:
                print(f"  {i:2d}. {status} '{positive}' → kotva: {anchor_reliability:.3f}, centroid: {centroid_reliability:.3f}")
            else:
                print(f"  {i:2d}. {status} '{positive}' → kotva: {anchor_reliability:.3f}")

        print()

        # Test negativních příkladů
        print("🔍 TESTOVÁNÍ NEGATIVNÍCH PŘÍKLADŮ:")
        negative_reliabilities = []
        negative_centroid_reliabilities = []

        for i, negative in enumerate(negatives, 1):
            negative_embedding = classifier.encode([negative])[0]

            # Spolehlivost proti kotvě
            anchor_similarity = cosine_similarity(
                anchor_embedding.reshape(1, -1),
                negative_embedding.reshape(1, -1)
            )[0][0]
            anchor_reliability = (anchor_similarity + 1) / 2
            #anchor_reliability = max(0.0, anchor_similarity)
            negative_reliabilities.append(anchor_reliability)

            # Spolehlivost proti centroidu (pokud existuje)
            centroid_reliability = None
            if category_centroid is not None:
                centroid_similarity = cosine_similarity(
                    category_centroid.reshape(1, -1),
                    negative_embedding.reshape(1, -1)
                )[0][0]
                #centroid_reliability = max(0.0, centroid_similarity)
                centroid_reliability = (centroid_similarity + 1) / 2
                negative_centroid_reliabilities.append(centroid_reliability)

            status = "✅" if anchor_reliability < 0.7 else "⚠️" if anchor_reliability < 0.8 else "❌"

            if centroid_reliability is not None:
                print(f"  {i:2d}. {status} '{negative}' → kotva: {anchor_reliability:.3f}, centroid: {centroid_reliability:.3f}")
            else:
                print(f"  {i:2d}. {status} '{negative}' → kotva: {anchor_reliability:.3f}")

        print()
        
        # Statistiky pro kategorii
        pos_avg = np.mean(positive_reliabilities) if positive_reliabilities else 0
        neg_avg = np.mean(negative_reliabilities) if negative_reliabilities else 0
        pos_correct = sum(1 for r in positive_reliabilities if r > 0.8)
        neg_correct = sum(1 for r in negative_reliabilities if r < 0.7)  # Pro negativní: nízká spolehlivost = správně

        print("📊 STATISTIKY KATEGORIE (KOTVA):")
        print(f"  Průměrná spolehlivost pozitivních: {pos_avg:.3f}")
        print(f"  Průměrná spolehlivost negativních: {neg_avg:.3f}")
        print(f"  Správně klasifikované pozitivní: {pos_correct}/{len(positives)} ({pos_correct/len(positives)*100:.1f}%)")
        print(f"  Správně klasifikované negativní: {neg_correct}/{len(negatives)} ({neg_correct/len(negatives)*100:.1f}%)")
        print(f"  Separace (pozitivní - negativní): {pos_avg - neg_avg:.3f}")

        # Statistiky pro centroidy (pokud existují)
        if positive_centroid_reliabilities and negative_centroid_reliabilities:
            pos_centroid_avg = np.mean(positive_centroid_reliabilities)
            neg_centroid_avg = np.mean(negative_centroid_reliabilities)
            pos_centroid_correct = sum(1 for r in positive_centroid_reliabilities if r > 0.8)
            neg_centroid_correct = sum(1 for r in negative_centroid_reliabilities if r < 0.7)

            print()
            print("📊 STATISTIKY KATEGORIE (CENTROID):")
            print(f"  Průměrná spolehlivost pozitivních: {pos_centroid_avg:.3f}")
            print(f"  Průměrná spolehlivost negativních: {neg_centroid_avg:.3f}")
            print(f"  Správně klasifikované pozitivní: {pos_centroid_correct}/{len(positives)} ({pos_centroid_correct/len(positives)*100:.1f}%)")
            print(f"  Správně klasifikované negativní: {neg_centroid_correct}/{len(negatives)} ({neg_centroid_correct/len(negatives)*100:.1f}%)")
            print(f"  Separace (pozitivní - negativní): {pos_centroid_avg - neg_centroid_avg:.3f}")

            # Srovnání kotvy vs centroidu
            print()
            print("🔄 SROVNÁNÍ KOTVA vs CENTROID:")
            print(f"  Pozitivní - zlepšení: {pos_centroid_avg - pos_avg:+.3f}")
            print(f"  Negativní - zlepšení: {neg_avg - neg_centroid_avg:+.3f} (nižší je lepší)")
            print(f"  Separace - zlepšení: {(pos_centroid_avg - neg_centroid_avg) - (pos_avg - neg_avg):+.3f}")

        # Doporučení
        print()
        if pos_avg - neg_avg < 0.1:
            print("  ⚠️  VAROVÁNÍ: Malá separace mezi pozitivními a negativními!")
        if pos_avg < 0.6:
            print("  ⚠️  VAROVÁNÍ: Nízká spolehlivost pozitivních příkladů!")
        if neg_avg > 0.6:
            print("  ⚠️  VAROVÁNÍ: Vysoká spolehlivost negativních příkladů (měla by být nízká)!")
        
        print()
        print("=" * 80)
        print()
        
        # Aktualizace celkových statistik
        overall_stats['categories'] += 1
        overall_stats['positive_correct'] += pos_correct
        overall_stats['positive_total'] += len(positives)
        overall_stats['negative_correct'] += neg_correct
        overall_stats['negative_total'] += len(negatives)
    
    classifier.save()

    # Celkové statistiky
    print("🏆 CELKOVÉ STATISTIKY:")
    print(f"  Testováno kategorií: {overall_stats['categories']}")
    print(f"  Celkem pozitivních příkladů: {overall_stats['positive_total']}")
    print(f"  Celkem negativních příkladů: {overall_stats['negative_total']}")
    
    if overall_stats['positive_total'] > 0:
        pos_accuracy = overall_stats['positive_correct'] / overall_stats['positive_total'] * 100
        print(f"  Úspěšnost pozitivních: {overall_stats['positive_correct']}/{overall_stats['positive_total']} ({pos_accuracy:.1f}%)")
    
    if overall_stats['negative_total'] > 0:
        neg_accuracy = overall_stats['negative_correct'] / overall_stats['negative_total'] * 100
        print(f"  Úspěšnost negativních: {overall_stats['negative_correct']}/{overall_stats['negative_total']} ({neg_accuracy:.1f}%)")
    
    if overall_stats['positive_total'] > 0 and overall_stats['negative_total'] > 0:
        total_correct = overall_stats['positive_correct'] + overall_stats['negative_correct']
        total_examples = overall_stats['positive_total'] + overall_stats['negative_total']
        total_accuracy = total_correct / total_examples * 100
        print(f"  Celková úspěšnost: {total_correct}/{total_examples} ({total_accuracy:.1f}%)")
    
    print()
    print("📝 LEGENDA:")
    print("  ✅ Správně klasifikováno (pozitivní > 0.7, negativní < 0.5)")
    print("  ⚠️  Hraničně klasifikováno (pozitivní 0.5-0.7, negativní 0.5-0.7)")
    print("  ❌ Špatně klasifikováno (pozitivní < 0.5, negativní > 0.7)")
    print()
    print("📊 SPOLEHLIVOST:")
    print("  Pozitivní: max(0, podobnost) - vyšší = lepší")
    print("  Negativní: max(0, podobnost) - nižší = lepší")
    print()
    print("🔄 SROVNÁNÍ:")
    print("  Kotva: Původní text kategorie z Excel souboru")
    print("  Centroid: Průměr všech pozitivních příkladů kategorie")
    print("  Centroid obvykle poskytuje lepší klasifikaci než kotva")
    print()


def main():
    """Hlavní funkce skriptu."""
    # Výchozí cesta k tréninkovým datům
    default_data_path = "training_data/training_set.xlsx"
    
    # Zkontrolujeme argumenty příkazové řádky
    if len(sys.argv) > 1:
        data_path = sys.argv[1]
    else:
        data_path = default_data_path
    
    # Zkontrolujeme existenci souboru
    if not os.path.exists(data_path):
        print(f"❌ Soubor '{data_path}' nenalezen!")
        print(f"Použití: python {sys.argv[0]} [cesta_k_excel_souboru]")
        print(f"Výchozí cesta: {default_data_path}")
        return
    
    try:
        test_classification_quality(data_path)
    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        import traceback
        traceback.print_exc()


def quick_test(data_path, threshold_positive=0.8, threshold_negative=0.7):
    """
    Rychlý test - pouze souhrnné statistiky bez detailního výpisu.

    Args:
        data_path (str): Cesta k Excel souboru
        threshold_positive (float): Práh spolehlivosti pro pozitivní příklady
        threshold_negative (float): Práh spolehlivosti pro negativní příklady
    """
    print("🚀 RYCHLÝ TEST KVALITY KLASIFIKACE")
    print("=" * 50)

    structured_data = load_training_data_from_xlsx(data_path)
    if not structured_data:
        print("❌ Nepodařilo se načíst data!")
        return

    classifier = KeyClassifier()

    total_pos_correct = 0
    total_pos_count = 0
    total_neg_correct = 0
    total_neg_count = 0

    for category_data in structured_data:
        anchor = category_data['anchor_triplet']
        positives = category_data['positives_variants']
        negatives = category_data['negatives']

        anchor_embedding = classifier.encode([anchor])[0]

        # Test pozitivních
        for positive in positives:
            pos_embedding = classifier.encode([positive])[0]
            similarity = cosine_similarity(
                anchor_embedding.reshape(1, -1),
                pos_embedding.reshape(1, -1)
            )[0][0]
            reliability = max(0.0, similarity)
            if reliability > threshold_positive:
                total_pos_correct += 1
            total_pos_count += 1

        # Test negativních
        for negative in negatives:
            neg_embedding = classifier.encode([negative])[0]
            similarity = cosine_similarity(
                anchor_embedding.reshape(1, -1),
                neg_embedding.reshape(1, -1)
            )[0][0]
            reliability = max(0.0, similarity)
            if reliability < threshold_negative:  # Pro negativní: nízká spolehlivost = správně
                total_neg_correct += 1
            total_neg_count += 1
    
    classifier.save()

    # Výsledky
    pos_acc = total_pos_correct / total_pos_count * 100 if total_pos_count > 0 else 0
    neg_acc = total_neg_correct / total_neg_count * 100 if total_neg_count > 0 else 0
    total_acc = (total_pos_correct + total_neg_correct) / (total_pos_count + total_neg_count) * 100

    print(f"📊 Kategorií: {len(structured_data)}")
    print(f"✅ Pozitivní: {total_pos_correct}/{total_pos_count} ({pos_acc:.1f}%)")
    print(f"❌ Negativní: {total_neg_correct}/{total_neg_count} ({neg_acc:.1f}%)")
    print(f"🎯 Celkem: {total_pos_correct + total_neg_correct}/{total_pos_count + total_neg_count} ({total_acc:.1f}%)")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        data_path = sys.argv[2] if len(sys.argv) > 2 else "training_data/training_set.xlsx"
        quick_test(data_path)
    else:
        #quick_test("training_data/training_set.xlsx")
        main()
