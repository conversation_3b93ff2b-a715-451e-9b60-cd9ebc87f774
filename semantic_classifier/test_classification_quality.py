"""
Zjednodušený skript pro testování kvality klasifikace s SBertClassifier.

Načte tréninková data z Excel souboru a otestuje klasifikaci
pozitivních a negativních příkladů. Všechna skóre jsou normalizovaná do rozsahu 0-1.
"""

import os
import sys
import numpy as np
from SBertClassifier import SBertClassifier
from training_data_preparation import load_training_data_from_xlsx


def test_classification_quality(data_path, threshold_positive=0.7, threshold_negative=0.6):
    """
    Otestuje kvalitu klasifikace na trénovacích datech s SBertClassifier.

    Args:
        data_path (str): Cesta k Excel souboru s tréninkovými daty
        threshold_positive (float): Práh pro pozitivní příklady (default: 0.7)
        threshold_negative (float): Práh pro negativní příklady (default: 0.6)
    """
    print("=" * 80)
    print("KONTROLA KVALITY KLASIFIKACE - SBertClassifier")
    print("=" * 80)
    print()

    # Načtení dat
    print(f"Načítám data z: {data_path}")
    class_info, structured_data = load_training_data_from_xlsx(data_path)

    if not structured_data:
        print("❌ Nepodařilo se načíst žádná data!")
        return

    print(f"✅ Načteno {len(structured_data)} kategorií")
    print(f"📊 Prahy: pozitivní > {threshold_positive}, negativní < {threshold_negative}")
    print()

    # Inicializace klasifikátoru
    print("Inicializuji SBertClassifier...")
    classifier = SBertClassifier()

    # Konverze dat do formátu pro SBertClassifier
    print("Konvertuji data do formátu pro SBertClassifier...")
    class_texts = {}
    for category_data in structured_data:
        category_name = category_data['category_name']
        anchor = category_data['anchor_triplet']
        positives = category_data['positives_variants']

        # Kombinujeme kotvu s pozitivními příklady
        texts = [anchor] + positives
        class_texts[category_name] = texts

    # Vytvoření prototypů z dat
    print("Vytvářím prototypy kategorií...")
    classifier.create_and_save_prototypes(class_texts)

    # Analýza každé kategorie
    overall_stats = {
        'categories': 0,
        'positive_correct': 0,
        'positive_total': 0,
        'negative_correct': 0,
        'negative_total': 0
    }

    for category_data in structured_data:
        category_name = category_data['category_name']
        anchor = category_data['anchor_triplet']
        positives = category_data['positives_variants']
        negatives = category_data['negatives']

        print(f"📂 KATEGORIE: {category_name}")
        print(f"🎯 KOTVA: '{anchor}'")
        print(f"✅ Pozitivní příklady: {len(positives)}")
        print(f"❌ Negativní příklady: {len(negatives)}")
        print("-" * 60)

        # Test pozitivních příkladů
        print("🔍 TESTOVÁNÍ POZITIVNÍCH PŘÍKLADŮ:")
        positive_scores = []

        for i, positive in enumerate(positives, 1):
            # Použijeme similarity metodu (už normalizovanou 0-1)
            similarity_score = classifier.similarity(anchor, positive)
            positive_scores.append(similarity_score)

            status = "✅" if similarity_score > threshold_positive else "⚠️" if similarity_score > 0.6 else "❌"
            print(f"  {i:2d}. {status} '{positive}' → {similarity_score:.3f}")

        print()

        # Test negativních příkladů
        print("🔍 TESTOVÁNÍ NEGATIVNÍCH PŘÍKLADŮ:")
        negative_scores = []

        for i, negative in enumerate(negatives, 1):
            # Použijeme similarity metodu (už normalizovanú 0-1)
            similarity_score = classifier.similarity(anchor, negative)
            negative_scores.append(similarity_score)

            status = "✅" if similarity_score < threshold_negative else "⚠️" if similarity_score < 0.7 else "❌"
            print(f"  {i:2d}. {status} '{negative}' → {similarity_score:.3f}")

        print()
        
        # Statistiky pro kategorii
        pos_avg = np.mean(positive_scores) if positive_scores else 0
        neg_avg = np.mean(negative_scores) if negative_scores else 0
        pos_correct = sum(1 for score in positive_scores if score > threshold_positive)
        neg_correct = sum(1 for score in negative_scores if score < threshold_negative)

        print("📊 STATISTIKY KATEGORIE:")
        print(f"  Průměrné skóre pozitivních: {pos_avg:.3f}")
        print(f"  Průměrné skóre negativních: {neg_avg:.3f}")
        print(f"  Správně klasifikované pozitivní: {pos_correct}/{len(positives)} ({pos_correct/len(positives)*100:.1f}%)")
        print(f"  Správně klasifikované negativní: {neg_correct}/{len(negatives)} ({neg_correct/len(negatives)*100:.1f}%)")
        print(f"  Separace (pozitivní - negativní): {pos_avg - neg_avg:.3f}")

        # Doporučení
        print()
        if pos_avg - neg_avg < 0.1:
            print("  ⚠️  VAROVÁNÍ: Malá separace mezi pozitivními a negativními!")
        if pos_avg < 0.6:
            print("  ⚠️  VAROVÁNÍ: Nízké skóre pozitivních příkladů!")
        if neg_avg > 0.6:
            print("  ⚠️  VAROVÁNÍ: Vysoké skóre negativních příkladů (mělo by být nízké)!")

        print()
        print("=" * 80)
        print()

        # Aktualizace celkových statistik
        overall_stats['categories'] += 1
        overall_stats['positive_correct'] += pos_correct
        overall_stats['positive_total'] += len(positives)
        overall_stats['negative_correct'] += neg_correct
        overall_stats['negative_total'] += len(negatives)
    # Celkové statistiky
    print("🏆 CELKOVÉ STATISTIKY:")
    print(f"  Testováno kategorií: {overall_stats['categories']}")
    print(f"  Celkem pozitivních příkladů: {overall_stats['positive_total']}")
    print(f"  Celkem negativních příkladů: {overall_stats['negative_total']}")

    if overall_stats['positive_total'] > 0:
        pos_accuracy = overall_stats['positive_correct'] / overall_stats['positive_total'] * 100
        print(f"  Úspěšnost pozitivních: {overall_stats['positive_correct']}/{overall_stats['positive_total']} ({pos_accuracy:.1f}%)")

    if overall_stats['negative_total'] > 0:
        neg_accuracy = overall_stats['negative_correct'] / overall_stats['negative_total'] * 100
        print(f"  Úspěšnost negativních: {overall_stats['negative_correct']}/{overall_stats['negative_total']} ({neg_accuracy:.1f}%)")

    if overall_stats['positive_total'] > 0 and overall_stats['negative_total'] > 0:
        total_correct = overall_stats['positive_correct'] + overall_stats['negative_correct']
        total_examples = overall_stats['positive_total'] + overall_stats['negative_total']
        total_accuracy = total_correct / total_examples * 100
        print(f"  Celková úspěšnost: {total_correct}/{total_examples} ({total_accuracy:.1f}%)")

    print()
    print("📝 LEGENDA:")
    print(f"  ✅ Správně klasifikováno (pozitivní > {threshold_positive}, negativní < {threshold_negative})")
    print("  ⚠️  Hraničně klasifikováno")
    print("  ❌ Špatně klasifikováno")
    print()
    print("📊 SKÓRE PODOBNOSTI:")
    print("  Rozsah: 0.0 - 1.0 (normalizované)")
    print("  Pozitivní příklady: vyšší skóre = lepší")
    print("  Negativní příklady: nižší skóre = lepší")
    print("  Separace: rozdíl mezi průměrným skóre pozitivních a negativních")
    print()


def main():
    """Hlavní funkce skriptu."""
    # Výchozí cesta k tréninkovým datům
    default_data_path = "training_data/training_set.xlsx"
    
    # Zkontrolujeme argumenty příkazové řádky
    if len(sys.argv) > 1:
        data_path = sys.argv[1]
    else:
        data_path = default_data_path
    
    # Zkontrolujeme existenci souboru
    if not os.path.exists(data_path):
        print(f"❌ Soubor '{data_path}' nenalezen!")
        print(f"Použití: python {sys.argv[0]} [cesta_k_excel_souboru]")
        print(f"Výchozí cesta: {default_data_path}")
        return
    
    try:
        test_classification_quality(data_path)
    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        import traceback
        traceback.print_exc()


def quick_test(data_path, threshold_positive=0.7, threshold_negative=0.6):
    """
    Rychlý test - pouze souhrnné statistiky bez detailního výpisu.

    Args:
        data_path (str): Cesta k Excel souboru
        threshold_positive (float): Práh pro pozitivní příklady
        threshold_negative (float): Práh pro negativní příklady
    """
    print("🚀 RYCHLÝ TEST KVALITY KLASIFIKACE - SBertClassifier")
    print("=" * 60)

    class_info, structured_data = load_training_data_from_xlsx(data_path)
    if not structured_data:
        print("❌ Nepodařilo se načíst data!")
        return

    classifier = SBertClassifier()

    # Konverze dat do formátu pro SBertClassifier
    class_texts = {}
    for category_data in structured_data:
        category_name = category_data['category_name']
        anchor = category_data['anchor_triplet']
        positives = category_data['positives_variants']
        texts = [anchor] + positives
        class_texts[category_name] = texts

    classifier.create_and_save_prototypes(class_texts)

    total_pos_correct = 0
    total_pos_count = 0
    total_neg_correct = 0
    total_neg_count = 0

    print(f"Testování {len(structured_data)} kategorií...")

    for category_data in structured_data:
        anchor = category_data['anchor_triplet']
        positives = category_data['positives_variants']
        negatives = category_data['negatives']

        # Test pozitivních
        for positive in positives:
            score = classifier.similarity(anchor, positive)
            if score > threshold_positive:
                total_pos_correct += 1
            total_pos_count += 1

        # Test negativních
        for negative in negatives:
            score = classifier.similarity(anchor, negative)
            if score < threshold_negative:  # Pro negativní: nízké skóre = správně
                total_neg_correct += 1
            total_neg_count += 1

    # Výsledky
    pos_acc = total_pos_correct / total_pos_count * 100 if total_pos_count > 0 else 0
    neg_acc = total_neg_correct / total_neg_count * 100 if total_neg_count > 0 else 0
    total_acc = (total_pos_correct + total_neg_correct) / (total_pos_count + total_neg_count) * 100

    print(f"📊 Kategorií: {len(structured_data)}")
    print(f"✅ Pozitivní: {total_pos_correct}/{total_pos_count} ({pos_acc:.1f}%)")
    print(f"❌ Negativní: {total_neg_correct}/{total_neg_count} ({neg_acc:.1f}%)")
    print(f"🎯 Celkem: {total_pos_correct + total_neg_correct}/{total_pos_count + total_neg_count} ({total_acc:.1f}%)")
    print(f"📏 Prahy: pozitivní > {threshold_positive}, negativní < {threshold_negative}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        data_path = sys.argv[2] if len(sys.argv) > 2 else "training_data/training_set.xlsx"
        quick_test(data_path)
    else:
        main()
