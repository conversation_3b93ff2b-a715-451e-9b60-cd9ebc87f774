from utils.utils import KEY_CLASS_REGISTRY


def do(df):
    import os
    import sys
    from semantic_classifier.KeyClassifier import KeyClassifier

    # Najdeme správnou cestu k modelu
    current_dir = os.getcwd()
    model_path = os.path.join(current_dir, 'semantic_classifier','mpnet')

    # Kontrola existence modelu
    if not os.path.exists(model_path):
        print(f"   ❌ CHYBA: Model nenalezen v cestě: {model_path}")
        print("   ❌ Key classification nelze provést bez natrénovaného modelu!")
        sys.exit(1)

    print(f"   ✓ Model nalezen v: {model_path}")

    # Inicializace klasifikátoru s explicitní cestou k modelu a prototypům
    classifier = KeyClassifier()
    #classifier.set_category_id_mapping(KEY_CLASS_REGISTRY)
    # Načtení mapování kategorií z Excel souboru
    print("   Loading category mapping from Excel...")

    # Klasifikace textů s číselnými identifikátory
    print("   Classifying texts...")

    # Získáme texty z DataFrame
    mask_value = df['value_class'] > 0
    texts = df.loc[~mask_value, 'text'].tolist()
    
    print(f"   ID instance KeyClassifier: {id(classifier)}")
    print(f"   Počet prototypů v KeyClassifier: {len(getattr(classifier, 'class_prototypes', {}))}")

    # Klasifikace pomocí nové KeyClassifier
    results = classifier.classify_batch(texts)

    df['key_class'] = 0
    df['similarity'] = 0

    idxs = df.index[~mask_value]
    for i, idx in enumerate(idxs):
        df.at[idx, 'key_class'] = results[i][0]
        df.at[idx, 'similarity'] = results[i][1]

    threshold = 0.85
    mask = df['similarity'] >= threshold
    df.loc[~mask, 'key_class'] = 0

    return df