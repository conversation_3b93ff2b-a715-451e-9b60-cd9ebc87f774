import os
import numpy as np
from typing import List, Optional, Dict, Tuple
from sentence_transformers import SentenceTransformer

class SBertClassifier:
    """
    Minimalistická třída pro sémantickou klasifikaci kompatibilní s C++.

    Po inicializaci se automaticky načte model a prototypy (pokud existují).
    Implementuje základní metody pro embeddingy, podobnost a správu prototypů.
    """

    def __init__(self, model_name: str = "sentence-transformers/paraphrase-multilingual-mpnet-base-v2",
                 model_dir: str = "./semantic_classifier/mpnet_ok", device: str = "cpu"):
        """
        Inicializuje klasifikátor a automaticky načte model a prototypy.

        Args:
            model_name: název modelu na HuggingFace
            model_dir: cesta k lokální složce s modelem
            device: 'cpu', 'cuda' nebo 'mps'
        """
        self.model_name = model_name
        self.model_dir = model_dir
        self.device = device

        # Inicializace atributů
        self.model: Optional[SentenceTransformer] = None
        self.embedding_dim: Optional[int] = None
        self.class_names: List[str] = []
        self.class_ids: List[int] = []
        self.proto_matrix: Optional[np.ndarray] = None  # (n_classes, emb_dim) normalizovaná

        # Automatické načtení modelu a prototypů
        self._load_model()
        self._load_prototypes()

        print(f"SBertClassifier inicializován (device: {self.device}, prototypy: {len(self.class_names)})")

    def _is_valid_model_dir(self, path: str) -> bool:
        """Kontroluje, zda složka obsahuje validní model."""
        if not os.path.isdir(path):
            return False
        files = os.listdir(path)
        return "config.json" in files and ("pytorch_model.bin" in files or "model.safetensors" in files)

    def _load_model(self):
        """Načte model z lokální složky nebo stáhne z HuggingFace."""
        if self._is_valid_model_dir(self.model_dir):
            print(f"Načítám lokální model ze složky: {self.model_dir}")
            self.model = SentenceTransformer(self.model_dir, device=self.device)
        else:
            print(f"Stahuji model z HuggingFace: {self.model_name}")
            self.model = SentenceTransformer(self.model_name, device=self.device)
            os.makedirs(self.model_dir, exist_ok=True)
            self.model.save(self.model_dir)
            print(f"Model uložen do: {self.model_dir}")

        # Zjistíme rozměr embeddingů
        self.embedding_dim = self.model.get_sentence_embedding_dimension()

    def _load_prototypes(self):
        """Načte prototypy, pokud existují."""
        proto_path = os.path.join(self.model_dir, "class_prototypes.npy")
        names_path = os.path.join(self.model_dir, "class_names.txt")
        ids_path = os.path.join(self.model_dir, "class_ids.txt")

        if all(os.path.isfile(p) for p in [proto_path, names_path, ids_path]):
            try:
                # Načteme data
                prototypes = np.load(proto_path).astype(np.float32)
                with open(names_path, 'r', encoding='utf-8') as f:
                    self.class_names = [line.strip() for line in f]
                with open(ids_path, 'r', encoding='utf-8') as f:
                    self.class_ids = [int(line.strip()) for line in f]

                # Normalizace prototypů
                norms = np.linalg.norm(prototypes, axis=1, keepdims=True)
                norms[norms == 0] = 1.0
                self.proto_matrix = prototypes / norms

                print(f"Prototypy načteny: {len(self.class_names)} tříd")
            except Exception as e:
                print(f"Chyba při načítání prototypů: {e}")
                self._reset_prototypes()
        else:
            self._reset_prototypes()

    def _reset_prototypes(self):
        """Resetuje prototypy na prázdné hodnoty."""
        self.proto_matrix = None
        self.class_names = []
        self.class_ids = []

    def encode(self, texts) -> np.ndarray:
        """
        Vypočítá embeddingy pro texty.

        Args:
            texts: string nebo seznam stringů

        Returns:
            numpy array s embeddingy (normalizované), shape: (n_texts, embedding_dim)
        """
        if self.model is None:
            raise RuntimeError("Model není načten.")

        if isinstance(texts, str):
            texts = [texts]

        # Vypočítáme embeddingy s normalizací
        embeddings = self.model.encode(texts, convert_to_tensor=False, normalize_embeddings=True)
        return np.array(embeddings, dtype=np.float32)

    def similarity(self, text1: str, text2: str) -> float:
        """
        Vypočítá kosinusovou podobnost mezi dvěma texty.

        Args:
            text1, text2: texty k porovnání

        Returns:
            kosinusová podobnost (0-1)
        """
        embeddings = self.encode([text1, text2])
        # Kosinusová podobnost normalizovaných vektorů je jen dot product
        return float(np.dot(embeddings[0], embeddings[1]))

    def create_and_save_prototypes(self, class_texts: Dict[str, List[str]]):
        """
        Vytvoří a uloží prototypy z trénovacích textů.

        Args:
            class_texts: dict {název_třídy: [seznam_textů]}
        """
        if not class_texts:
            raise ValueError("class_texts nesmí být prázdné")

        print(f"Vytvářím prototypy pro {len(class_texts)} tříd...")

        # Připravíme seznamy
        proto_list = []
        self.class_names = []
        self.class_ids = []

        for class_id, (class_name, texts) in enumerate(class_texts.items()):
            if not texts:
                raise ValueError(f"Třída '{class_name}' nemá žádné texty")

            # Vypočítáme embeddingy a centroid
            embeddings = self.encode(texts)
            centroid = np.mean(embeddings, axis=0).astype(np.float32)

            # Normalizace
            norm = np.linalg.norm(centroid)
            if norm > 0:
                centroid = centroid / norm

            proto_list.append(centroid)
            self.class_names.append(class_name)
            self.class_ids.append(class_id)

        # Vytvoříme matici prototypů
        self.proto_matrix = np.array(proto_list, dtype=np.float32)

        # Uložíme do souborů
        self._save_prototypes()
        print(f"Prototypy vytvořeny a uloženy: {len(self.class_names)} tříd")

    def _save_prototypes(self):
        """Uloží prototypy do souborů."""
        if self.proto_matrix is None:
            return

        os.makedirs(self.model_dir, exist_ok=True)

        # Uložíme matici prototypů
        np.save(os.path.join(self.model_dir, "class_prototypes.npy"), self.proto_matrix)

        # Uložíme názvy tříd
        with open(os.path.join(self.model_dir, "class_names.txt"), 'w', encoding='utf-8') as f:
            for name in self.class_names:
                f.write(f"{name}\n")

        # Uložíme ID tříd
        with open(os.path.join(self.model_dir, "class_ids.txt"), 'w', encoding='utf-8') as f:
            for class_id in self.class_ids:
                f.write(f"{class_id}\n")

    def classify(self, text: str, return_confidence: bool = True) -> Tuple[int, float]:
        """
        Klasifikuje jeden text pomocí prototypů.

        Args:
            text: text ke klasifikaci
            return_confidence: zda vrátit také confidence score

        Returns:
            (class_id, confidence_score) nebo jen class_id
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny. Spusťte create_and_save_prototypes().")

        # Vypočítáme embedding textu
        text_embedding = self.encode([text])[0]  # shape: (embedding_dim,)

        # Kosinusová podobnost s všemi prototypy
        similarities = np.dot(self.proto_matrix, text_embedding)  # shape: (n_classes,)

        # Najdeme nejlepší třídu
        best_idx = np.argmax(similarities)
        best_class_id = self.class_ids[best_idx]
        best_score = float(similarities[best_idx])

        if return_confidence:
            return best_class_id, best_score
        else:
            return best_class_id

    def classify_batch(self, texts: List[str]) -> List[Tuple[int, float]]:
        """
        Vektorizovaná klasifikace více textů najednou.

        Args:
            texts: seznam textů ke klasifikaci

        Returns:
            seznam (class_id, confidence_score) pro každý text
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")

        if not texts:
            return []

        # Vypočítáme embeddingy všech textů
        text_embeddings = self.encode(texts)  # shape: (n_texts, embedding_dim)

        # Vektorizovaný výpočet podobností
        similarities = np.dot(text_embeddings, self.proto_matrix.T)  # shape: (n_texts, n_classes)

        # Najdeme nejlepší třídy
        best_indices = np.argmax(similarities, axis=1)  # shape: (n_texts,)
        best_scores = similarities[np.arange(len(texts)), best_indices]

        # Převedeme na výsledky
        results = []
        for i, (idx, score) in enumerate(zip(best_indices, best_scores)):
            class_id = self.class_ids[idx]
            results.append((class_id, float(score)))

        return results

    def get_class_name(self, class_id: int) -> str:
        """Vrátí název třídy pro dané ID."""
        try:
            idx = self.class_ids.index(class_id)
            return self.class_names[idx]
        except ValueError:
            raise ValueError(f"Neznámé class_id: {class_id}")

    def get_class_id(self, class_name: str) -> int:
        """Vrátí ID třídy pro daný název."""
        try:
            idx = self.class_names.index(class_name)
            return self.class_ids[idx]
        except ValueError:
            raise ValueError(f"Neznámý class_name: {class_name}")

    def get_prototype_matrix(self) -> np.ndarray:
        """
        Vrátí matici prototypů pro export do C++.

        Returns:
            numpy array shape: (n_classes, embedding_dim)
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")
        return self.proto_matrix.copy()

    def export_for_cpp(self, output_path: str):
        """
        Exportuje prototypy v binárním formátu kompatibilním s C++.

        Args:
            output_path: cesta k výstupnímu souboru
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")

        with open(output_path, 'wb') as f:
            # Header: počet tříd, rozměr embeddingů
            f.write(len(self.class_names).to_bytes(4, 'little'))
            f.write(self.embedding_dim.to_bytes(4, 'little'))

            # Pro každou třídu: délka názvu, název, embedding
            for i, class_name in enumerate(self.class_names):
                name_bytes = class_name.encode('utf-8')
                f.write(len(name_bytes).to_bytes(4, 'little'))
                f.write(name_bytes)
                f.write(self.proto_matrix[i].tobytes())

        print(f"Prototypy exportovány do: {output_path}")

    def info(self):
        """Vypíše informace o klasifikátoru."""
        print(f"=== SBertClassifier Info ===")
        print(f"Model: {self.model_name}")
        print(f"Model dir: {self.model_dir}")
        print(f"Device: {self.device}")
        print(f"Embedding dim: {self.embedding_dim}")
        print(f"Počet tříd: {len(self.class_names)}")
        if self.class_names:
            print(f"Třídy: {', '.join(self.class_names)}")
        print(f"Prototypy načteny: {'Ano' if self.proto_matrix is not None else 'Ne'}")
        print("=" * 30)
