import os
import json
import numpy as np
import torch
from typing import List, Optional, Dict, Tuple
from sentence_transformers import SentenceTransformer

class SBertClassifier:
    """
    Minimalistická třída pro sémantickou klasifikaci kompatibilní s C++.

    Po inicializaci se automaticky načte model a prototypy (pokud existují).
    Implementuje základní metody pro embeddingy, podobnost a správu prototypů.
    """

    def __init__(self, model_name: str = "sentence-transformers/paraphrase-multilingual-mpnet-base-v2",
                 model_dir: str = "model_bert", device: str = "cpu"):
        """
        Inicializuje klasifikátor a automaticky načte model a prototypy.

        Args:
            model_name: název modelu na HuggingFace
            model_dir: cesta k lokální složce s modelem
            device: 'cpu', 'cuda' nebo 'mps'
        """
        self.model_name = model_name
        self.model_dir = model_dir
        self.device = device

        # Inicializace atributů
        self.model: Optional[SentenceTransformer] = None
        self.embedding_dim: Optional[int] = None
        self.class_names: List[str] = []
        self.class_ids: List[int] = []
        self.proto_matrix: Optional[np.ndarray] = None  # (n_classes, emb_dim) normalizovaná

        # Automatické načtení modelu a prototypů
        self._load_model()
        self._load_prototypes()

        print(f"SBertClassifier inicializován (device: {self.device}, prototypy: {len(self.class_names)})")

    def _is_valid_model_dir(self, path: str) -> bool:
        """Kontroluje, zda složka obsahuje validní model."""
        if not os.path.isdir(path):
            return False
        files = os.listdir(path)
        return "config.json" in files and ("pytorch_model.bin" in files or "model.safetensors" in files)

    def _load_model(self):
        """Načte model z lokální složky nebo stáhne z HuggingFace."""
        if self._is_valid_model_dir(self.model_dir):
            print(f"Načítám lokální model ze složky: {self.model_dir}")
            self.model = SentenceTransformer(self.model_dir, device=self.device)
        else:
            print(f"Stahuji model z HuggingFace: {self.model_name}")
            self.model = SentenceTransformer(self.model_name, device=self.device)
            os.makedirs(self.model_dir, exist_ok=True)
            self.model.save(self.model_dir)
            print(f"Model uložen do: {self.model_dir}")

        # Zjistíme rozměr embeddingů
        self.embedding_dim = self.model.get_sentence_embedding_dimension()

    def _load_prototypes(self):
        """Načte prototypy, pokud existují."""
        proto_path = os.path.join(self.model_dir, "class_prototypes.npy")
        names_path = os.path.join(self.model_dir, "class_names.txt")
        ids_path = os.path.join(self.model_dir, "class_ids.txt")

        if all(os.path.isfile(p) for p in [proto_path, names_path, ids_path]):
            try:
                # Načteme data
                prototypes = np.load(proto_path).astype(np.float32)
                with open(names_path, 'r', encoding='utf-8') as f:
                    self.class_names = [line.strip() for line in f]
                with open(ids_path, 'r', encoding='utf-8') as f:
                    self.class_ids = [int(line.strip()) for line in f]

                # Normalizace prototypů
                norms = np.linalg.norm(prototypes, axis=1, keepdims=True)
                norms[norms == 0] = 1.0
                self.proto_matrix = prototypes / norms

                print(f"Prototypy načteny: {len(self.class_names)} tříd")
            except Exception as e:
                print(f"Chyba při načítání prototypů: {e}")
                self._reset_prototypes()
        else:
            self._reset_prototypes()

    def _reset_prototypes(self):
        """Resetuje prototypy na prázdné hodnoty."""
        self.proto_matrix = None
        self.class_names = []
        self.class_ids = []

    def encode(self, texts) -> np.ndarray:
        """
        Vypočítá embeddingy pro texty.

        Args:
            texts: string nebo seznam stringů

        Returns:
            numpy array s embeddingy (normalizované), shape: (n_texts, embedding_dim)
        """
        if self.model is None:
            raise RuntimeError("Model není načten.")

        if isinstance(texts, str):
            texts = [texts]

        # Vypočítáme embeddingy s normalizací
        embeddings = self.model.encode(texts, convert_to_tensor=False, normalize_embeddings=True)
        return np.array(embeddings, dtype=np.float32)

    def similarity(self, text1: str, text2: str) -> float:
        """
        Vypočítá kosinusovou podobnost mezi dvěma texty.

        Args:
            text1, text2: texty k porovnání

        Returns:
            normalizovaná podobnost v rozsahu 0-1
        """
        embeddings = self.encode([text1, text2])
        # Kosinusová podobnost normalizovaných vektorů je jen dot product
        cosine_sim = float(np.dot(embeddings[0], embeddings[1]))
        # Normalizace z rozsahu [-1, 1] na [0, 1] s clampingem
        normalized = (cosine_sim + 1.0) / 2.0
        return max(0.0, min(1.0, normalized))

    def create_and_save_prototypes(self, class_texts: Dict[str, List[str]]):
        """
        Vytvoří a uloží prototypy z trénovacích textů.

        Args:
            class_texts: dict {název_třídy: [seznam_textů]}
        """
        if not class_texts:
            raise ValueError("class_texts nesmí být prázdné")

        print(f"Vytvářím prototypy pro {len(class_texts)} tříd...")

        # Připravíme seznamy
        proto_list = []
        self.class_names = []
        self.class_ids = []

        for class_id, (class_name, texts) in enumerate(class_texts.items()):
            if not texts:
                raise ValueError(f"Třída '{class_name}' nemá žádné texty")

            # Vypočítáme embeddingy a centroid
            embeddings = self.encode(texts)
            centroid = np.mean(embeddings, axis=0).astype(np.float32)

            # Normalizace
            norm = np.linalg.norm(centroid)
            if norm > 0:
                centroid = centroid / norm

            proto_list.append(centroid)
            self.class_names.append(class_name)
            self.class_ids.append(class_id)

        # Vytvoříme matici prototypů
        self.proto_matrix = np.array(proto_list, dtype=np.float32)

        # Uložíme do souborů
        self._save_prototypes()
        print(f"Prototypy vytvořeny a uloženy: {len(self.class_names)} tříd")

    def _save_prototypes(self):
        """Uloží prototypy do souborů."""
        if self.proto_matrix is None:
            return

        os.makedirs(self.model_dir, exist_ok=True)

        # Uložíme matici prototypů
        np.save(os.path.join(self.model_dir, "class_prototypes.npy"), self.proto_matrix)

        # Uložíme názvy tříd
        with open(os.path.join(self.model_dir, "class_names.txt"), 'w', encoding='utf-8') as f:
            for name in self.class_names:
                f.write(f"{name}\n")

        # Uložíme ID tříd
        with open(os.path.join(self.model_dir, "class_ids.txt"), 'w', encoding='utf-8') as f:
            for class_id in self.class_ids:
                f.write(f"{class_id}\n")

    def classify(self, text: str, return_confidence: bool = True) -> Tuple[int, float]:
        """
        Klasifikuje jeden text pomocí prototypů.

        Args:
            text: text ke klasifikaci
            return_confidence: zda vrátit také confidence score

        Returns:
            (class_id, normalized_confidence_score) nebo jen class_id
            confidence_score je normalizovaný do rozsahu 0-1
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny. Spusťte create_and_save_prototypes().")

        # Vypočítáme embedding textu
        text_embedding = self.encode([text])[0]  # shape: (embedding_dim,)

        # Kosinusová podobnost s všemi prototypy
        similarities = np.dot(self.proto_matrix, text_embedding)  # shape: (n_classes,)

        # Najdeme nejlepší třídu
        best_idx = np.argmax(similarities)
        best_class_id = self.class_ids[best_idx]
        best_score = float(similarities[best_idx])

        # Normalizace skóre z rozsahu [-1, 1] na [0, 1] s clampingem
        normalized_score = max(0.0, min(1.0, (best_score + 1.0) / 2.0))

        if return_confidence:
            return best_class_id, normalized_score
        else:
            return best_class_id

    def classify_batch(self, texts: List[str]) -> List[Tuple[int, float]]:
        """
        Vektorizovaná klasifikace více textů najednou.

        Args:
            texts: seznam textů ke klasifikaci

        Returns:
            seznam (class_id, normalized_confidence_score) pro každý text
            confidence_score je normalizovaný do rozsahu 0-1
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")

        if not texts:
            return []

        # Vypočítáme embeddingy všech textů
        text_embeddings = self.encode(texts)  # shape: (n_texts, embedding_dim)

        # Vektorizovaný výpočet podobností
        similarities = np.dot(text_embeddings, self.proto_matrix.T)  # shape: (n_texts, n_classes)

        # Najdeme nejlepší třídy
        best_indices = np.argmax(similarities, axis=1)  # shape: (n_texts,)
        best_scores = similarities[np.arange(len(texts)), best_indices]

        # Normalizace skóre z rozsahu [-1, 1] na [0, 1] s clampingem
        normalized_scores = np.clip((best_scores + 1.0) / 2.0, 0.0, 1.0)

        # Převedeme na výsledky
        results = []
        for i, (idx, score) in enumerate(zip(best_indices, normalized_scores)):
            class_id = self.class_ids[idx]
            results.append((class_id, float(score)))

        return results

    def get_class_name(self, class_id: int) -> str:
        """Vrátí název třídy pro dané ID."""
        try:
            idx = self.class_ids.index(class_id)
            return self.class_names[idx]
        except ValueError:
            raise ValueError(f"Neznámé class_id: {class_id}")

    def get_class_id(self, class_name: str) -> int:
        """Vrátí ID třídy pro daný název."""
        try:
            idx = self.class_names.index(class_name)
            return self.class_ids[idx]
        except ValueError:
            raise ValueError(f"Neznámý class_name: {class_name}")

    def get_prototype_matrix(self) -> np.ndarray:
        """
        Vrátí matici prototypů pro export do C++.

        Returns:
            numpy array shape: (n_classes, embedding_dim)
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")
        return self.proto_matrix.copy()

    def export_for_cpp(self, base_path: str, export_model: bool = True):
        """
        Kompletní export pro C++ - model, prototypy, tokenizer a metadata.

        Args:
            base_path: cesta k výstupní složce
            export_model: zda exportovat také ONNX model (může být pomalé)
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")

        os.makedirs(base_path, exist_ok=True)
        print(f"Exportuji do: {base_path}")

        # 1. Export prototypů jako raw binary (nejrychlejší pro C++)
        self._export_prototypes_raw(os.path.join(base_path, "prototypes.bin"))

        # 2. Export ONNX modelu (volitelné - může být pomalé)
        if export_model:
            try:
                self._export_text_encoder_onnx(base_path)
                print("✅ ONNX model exportován")
            except Exception as e:
                print(f"❌ ONNX export selhal: {e}")
                print("💡 Zkuste: pip install torch onnx optimum")

        # 3. Export tokenizer files
        self._export_tokenizer_files(base_path)

        # 4. C++ header s metadata
        self._generate_cpp_header(os.path.join(base_path, "model_config.h"))

        # 5. Jednoduchý binární export prototypů (kompatibilní s původní metodou)
        self._export_prototypes_simple(os.path.join(base_path, "prototypes_simple.bin"))

        print(f"\n📁 Export dokončen do: {base_path}")
        print("📝 Pro C++: použijte prototypes.bin + model_config.h")
        if export_model:
            print("📝 Pro plnou funkcionalnost: text_encoder.onnx + tokenizer/")

    def _export_prototypes_raw(self, filepath: str):
        """
        Export prototypů jako raw float32 array pro rychlé načtení v C++.
        Format: [float32 matrix: num_classes × embedding_dim]
        """
        if self.proto_matrix is None:
            raise RuntimeError("Žádné prototypy k exportu")

        # Uložit jako raw binary float32
        self.proto_matrix.astype(np.float32).tofile(filepath)

        # Uložit rozměry vedle
        dims_path = filepath.replace('.bin', '_dims.txt')
        with open(dims_path, 'w') as f:
            f.write(f"{len(self.class_names)} {self.embedding_dim}")

        print(f"Raw prototypy: {len(self.class_names)} tříd × {self.embedding_dim} dimenzí")
        print(f"  Soubor: {filepath}")
        print(f"  Rozměry: {dims_path}")

    def _export_prototypes_simple(self, filepath: str):
        """
        Jednoduchý binární export (kompatibilní s původní metodou).
        Format: [num_classes][embedding_dim][class1_name_len][class1_name][class1_embedding]...
        """
        with open(filepath, 'wb') as f:
            # Header: počet tříd, rozměr embeddingů
            f.write(len(self.class_names).to_bytes(4, 'little'))
            f.write(self.embedding_dim.to_bytes(4, 'little'))

            # Pro každou třídu: délka názvu, název, embedding
            for i, class_name in enumerate(self.class_names):
                name_bytes = class_name.encode('utf-8')
                f.write(len(name_bytes).to_bytes(4, 'little'))
                f.write(name_bytes)
                f.write(self.proto_matrix[i].tobytes())

        print(f"Jednoduché prototypy: {filepath}")

    def _export_text_encoder_onnx(self, base_path: str):
        """
        Exportuje pouze text encoding část sentence transformeru do ONNX.
        Optimalizováno pro text → embedding inference v C++.
        """
        if self.model is None:
            raise RuntimeError("Model není načten")

        try:
            # Získej underlying transformer model
            transformer_model = self.model[0].auto_model  # První modul
            tokenizer = self.model.tokenizer

            # Dummy input pro ONNX tracing
            sample_text = "Hello world"
            inputs = tokenizer(sample_text, return_tensors="pt", padding=True, truncation=True)

            # Nastavit model do eval módu
            transformer_model.eval()

            # ONNX export
            onnx_path = os.path.join(base_path, "text_encoder.onnx")

            with torch.no_grad():
                torch.onnx.export(
                    transformer_model,
                    (inputs['input_ids'], inputs['attention_mask']),
                    onnx_path,
                    input_names=['input_ids', 'attention_mask'],
                    output_names=['embeddings'],
                    dynamic_axes={
                        'input_ids': {0: 'batch_size', 1: 'sequence_length'},
                        'attention_mask': {0: 'batch_size', 1: 'sequence_length'},
                        'embeddings': {0: 'batch_size'}
                    },
                    opset_version=14,  # Novější verze pro lepší kompatibilitu
                    do_constant_folding=True
                )
            print(f"ONNX model: {onnx_path}")

        except ImportError as e:
            raise RuntimeError(f"ONNX export vyžaduje torch: {e}")
        except Exception as e:
            raise RuntimeError(f"ONNX export selhal: {e}")

    def _export_tokenizer_files(self, base_path: str):
        """
        Exportuje tokenizer files pro C++ (vocab, merges, config).
        """
        if self.model is None:
            return

        try:
            tokenizer = self.model.tokenizer
            tokenizer_path = os.path.join(base_path, "tokenizer")

            # Uložit tokenizer files
            tokenizer.save_pretrained(tokenizer_path)

            # Dodatečné info pro C++
            tokenizer_info = {
                "model_max_length": tokenizer.model_max_length,
                "vocab_size": len(tokenizer),
                "pad_token_id": tokenizer.pad_token_id,
                "cls_token_id": getattr(tokenizer, 'cls_token_id', None),
                "sep_token_id": getattr(tokenizer, 'sep_token_id', None),
            }

            with open(os.path.join(base_path, "tokenizer_config.json"), 'w') as f:
                json.dump(tokenizer_info, f, indent=2)

            print(f"Tokenizer: {tokenizer_path}")

        except Exception as e:
            print(f"⚠️  Tokenizer export selhal: {e}")

    def _generate_cpp_header(self, header_path: str):
        """
        Generuje C++ header s konstantami a strukturami.
        """
        header_content = f"""#ifndef MODEL_CONFIG_H
#define MODEL_CONFIG_H

// Auto-generated model configuration for SBertClassifier
namespace ModelConfig {{
    constexpr int EMBEDDING_DIM = {self.embedding_dim};
    constexpr int NUM_CLASSES = {len(self.class_names)};
    constexpr int MAX_SEQUENCE_LENGTH = 512;  // Adjust based on your model

    // Class names (ordered as in prototypes matrix)
    const char* CLASS_NAMES[NUM_CLASSES] = {{
        {', '.join([f'"{name}"' for name in self.class_names])}
    }};

    // Class IDs (ordered as in prototypes matrix)
    const int CLASS_IDS[NUM_CLASSES] = {{
        {', '.join([str(id) for id in self.class_ids])}
    }};

    // File paths
    constexpr const char* ONNX_MODEL_PATH = "text_encoder.onnx";
    constexpr const char* PROTOTYPES_PATH = "prototypes.bin";
    constexpr const char* PROTOTYPES_DIMS_PATH = "prototypes_dims.txt";
    constexpr const char* PROTOTYPES_SIMPLE_PATH = "prototypes_simple.bin";
    constexpr const char* TOKENIZER_PATH = "tokenizer";
}}

// Suggested C++ structures
struct ClassificationResult {{
    int class_id;
    const char* class_name;
    float confidence;
}};

struct PrototypeMatrix {{
    float* data;           // Raw float32 array [num_classes * embedding_dim]
    int num_classes;       // Number of classes
    int embedding_dim;     // Embedding dimension
}};

// Usage example:
// 1. Load prototypes: PrototypeMatrix matrix = load_prototypes("prototypes.bin");
// 2. Compute text embedding: float* embedding = encode_text("some text");
// 3. Classify: ClassificationResult result = classify(embedding, matrix);

#endif // MODEL_CONFIG_H"""

        with open(header_path, 'w') as f:
            f.write(header_content)

        print(f"C++ header: {header_path}")

    def info(self):
        """Vypíše informace o klasifikátoru."""
        print(f"=== SBertClassifier Info ===")
        print(f"Model: {self.model_name}")
        print(f"Model dir: {self.model_dir}")
        print(f"Device: {self.device}")
        print(f"Embedding dim: {self.embedding_dim}")
        print(f"Počet tříd: {len(self.class_names)}")
        if self.class_names:
            print(f"Třídy: {', '.join(self.class_names)}")
        print(f"Prototypy načteny: {'Ano' if self.proto_matrix is not None else 'Ne'}")
        print("=" * 30)
