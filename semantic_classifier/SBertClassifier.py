import os
import json
import numpy as np
from typing import List, Optional
from sentence_transformers import SentenceTransformer

model_name = "sentence-transformers/LaBSE"
model_dir = "./semantic_classifier/mpnet"

class SBertClassifier:
    def __init__(self, model_name: str = model_name, model_dir: str = model_dir, device: str = "cpu"):
        """
        model_name: jméno modelu na HuggingFace nebo lokální adresář
        model_dir: cesta k lokální slo<PERSON>, kde bude model uložen
        device: 'cpu' nebo 'cuda'
        """
        self.model_name = model_name
        self.model_dir = model_dir
        self.device = device

        self.model: Optional[SentenceTransformer] = None
        self.class_names: List[str] = []
        self.class_ids: List[int] = []
        self.proto_matrix: Optional[np.ndarray] = None  # (n_classes, emb_dim) normalizovaná
        self.embedding_dim: Optional[int] = None

        self._load_model()
        self._load_prototypes_if_exists()

    def _load_model(self):
        """
        Načte model z lok<PERSON>lní <PERSON>, pokud exist<PERSON>, jinak st<PERSON>.
        """
        if os.path.isdir(self.model_dir) and os.listdir(self.model_dir):
            self.model = SentenceTransformer(self.model_dir, device=self.device)
        else:
            # stáhne a uloží lokálně
            self.model = SentenceTransformer(self.model_name, device=self.device)
            os.makedirs(self.model_dir, exist_ok=True)
            self.model.save(self.model_dir)

        # ulož si embedding dim kvůli validacím
        self.embedding_dim = self.model.get_sentence_embedding_dimension()

    def _load_prototypes_if_exists(self):
        """
        Pokud existují uložené prototypy, načte je a připraví normalizovanou matici.
        """
        proto_path = os.path.join(self.model_dir, "class_prototypes.npy")
        names_path = os.path.join(self.model_dir, "class_names.txt")
        ids_path = os.path.join(self.model_dir, "class_ids.txt")

        if os.path.isfile(proto_path) and os.path.isfile(names_path) and os.path.isfile(ids_path):
            prototypes = np.load(proto_path)  # shape: (n_classes, emb_dim)
            self.class_names = [line.strip() for line in open(names_path, encoding="utf-8")]
            self.class_ids = [int(line.strip()) for line in open(ids_path, encoding="utf-8")]

            # normalizace na jednotkovou délku
            norms = np.linalg.norm(prototypes, axis=1, keepdims=True)
            norms[norms == 0] = 1
            self.proto_matrix = prototypes / norms
        else:
            self.proto_matrix = None
            self.class_names = []
            self.class_ids = []

    def _ensure_prototypes_loaded(self):
        """
        Vyhodí chybu, pokud nejsou načteny prototypy.
        """
        if self.proto_matrix is None:
            raise RuntimeError("Class prototypes nejsou načteny. Nelze použít tuto metodu.")

    def create_and_save_prototypes(self, class_texts: Dict[str, List[str]]):
        """
        Vytvoří prototypy z dodaných textů pro každou třídu a uloží je do složky s modelem.

        Parametry:
            class_texts: dict, kde klíč = název třídy, hodnota = seznam textů patřících do třídy.

        Výsledek:
            - vytvoří self.prototypes a self.proto_matrix
            - uloží je do 'prototypes.json' ve složce s modelem
        """
        if not class_texts:
            raise ValueError("class_texts je prázdné – nejsou žádné texty pro vytvoření prototypů.")

        # Embedujeme všechny texty po třídách
        self.prototypes = {}
        proto_matrix_list = []
        class_labels = []

        for cls, texts in class_texts.items():
            if not texts:
                raise ValueError(f"Třída '{cls}' nemá žádné texty.")

            embeddings = self.model.encode(texts, convert_to_numpy=True, device=self.device)
            centroid = np.mean(embeddings, axis=0)
            norm = np.linalg.norm(centroid)
            if norm != 0:
                centroid = centroid / norm

            self.prototypes[cls] = centroid.tolist()
            proto_matrix_list.append(centroid)
            class_labels.append(cls)

        # Uložíme prototypy
        proto_path = os.path.join(self.model_dir, "prototypes.json")
        with open(proto_path, "w", encoding="utf-8") as f:
            json.dump({"labels": class_labels, "vectors": self.prototypes}, f, ensure_ascii=False, indent=2)

        # Nastavíme interní matici
        self.proto_matrix = np.array(proto_matrix_list, dtype=np.float32)
