#!/usr/bin/env python3
"""
Standalone fine-tuning script pro semantic classifier.
Extrahovaná funkcionalita z Classifier.fine_tune() metody.

Tento skript umožňuje fine-tuning sentence transformer modelu
bez nutnosti vytváření instance Classifier třídy.
"""

import os
import re
import gc
import pickle
import torch
import numpy as np
from torch.utils.data import DataLoader, random_split
from sentence_transformers import SentenceTransformer, losses
from sentence_transformers.evaluation import TripletEvaluator
from tqdm.autonotebook import tqdm

from KeyClassifier import KeyClassifier
from training_data_preparation import load_training_data_from_xlsx, prepare_training_data


def detect_device():
    """
    Detekuje nejlepší dostupné zařízení pro trénink.
    
    Returns:
        str: 'cuda', 'mps', nebo 'cpu'
    """
    if torch.cuda.is_available():
        return 'cuda'
    # elif torch.backends.mps.is_built() and torch.backends.mps.is_available():
    #     print("Používám Apple Metal Performance Accelerators (MPS)")
    #     return 'mps'
    else:
        return 'cpu'


def preprocess_text(text):
    """
    Preprocessing funkce pro normalizaci textů před jejich zpracováním.
    Aplikuje se na všechny texty před kódováním do embeddingů.

    Args:
        text (str): Vstupní text k preprocessing.

    Returns:
        str: Preprocessovaný text.
    """
    if not isinstance(text, str):
        return str(text) if text is not None else ""

    text = re.sub(r'[.,:;—\-\'"+/&<>~¢|®©]', ' ', text)
    text = re.sub(r'\*', ' ', text).strip()
    text = re.sub(r'\(', ' ', text).strip()
    text = re.sub(r'\)', ' ', text).strip()
    text = re.sub(r'\[', ' ', text).strip()
    text = re.sub(r'\]', ' ', text).strip()
    text = re.sub(r'\\', ' ', text).strip()
    text = re.sub(r'\!', ' ', text).strip()
    text = re.sub(r'\?', ' ', text).strip()
    text = re.sub(r'\s+', ' ', text).strip()               # Collapse multiple spaces
    return text


def encode_texts(model, texts, device, batch_size=16):
    """
    Generuje embeddingy pro seznam textů.
    Automaticky aplikuje preprocessing na všechny texty před kódováním.

    Args:
        model: SentenceTransformer model
        texts (list): Seznam textových řetězců k zakódování.
        device (str): Zařízení pro výpočet
        batch_size (int): Velikost dávky pro zpracování.

    Returns:
        numpy.ndarray: Pole embeddingů.
    """
    if not isinstance(texts, list):
        texts = [texts]  # Zajistíme, že vstup je seznam

    # Aplikujeme preprocessing na všechny texty
    preprocessed_texts = [preprocess_text(text) for text in texts]

    # Adjust batch size for MPS to avoid memory issues
    actual_batch_size = batch_size
    if device == 'mps':
        # Use a smaller batch size on MPS to avoid memory issues
        actual_batch_size = min(batch_size, 32)  # Limit batch size on MPS
        if len(preprocessed_texts) > actual_batch_size and batch_size > actual_batch_size:
            print(f"Using reduced batch size {actual_batch_size} for encoding on MPS device (original: {batch_size})")

    return model.encode(preprocessed_texts, convert_to_numpy=True, batch_size=actual_batch_size, show_progress_bar=False)


def calculate_category_centroids(model, structured_data, device, save_path=None):
    """
    Vypočítá a uloží centroidy pro každou sémantickou kategorii
    na základě průměru embeddingů kotevní fráze a jejích pozitivních příkladů.

    Args:
        model: SentenceTransformer model
        structured_data (list): Seznam slovníků ve formátu
                              {'category_name': '...', 'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}.
        device (str): Zařízení pro výpočet
        save_path (str, optional): Cesta pro uložení vypočtených centroidů. Pokud None, centroidy se neuloží na disk.
        
    Returns:
        dict: Slovník centroidů kategorií
    """
    print("Vypočítávám centroidy kategorií...")
    if not structured_data:
        print("Nenalezena strukturovaná data pro výpočet centroidů.")
        return None

    # Shromáždíme všechny fráze a vytvoříme mapování indexů na kategorie
    all_phrases_for_encoding = []
    category_index_map = {}
    current_embedding_index = 0

    for item in structured_data:
        # Použijeme název listu jako název kategorie
        category_name = item['category_name']
        # Použijeme anchor_triplet a positives_variants jako fráze pro výpočet centroidu
        phrases = [item['anchor_triplet']] + item['positives_variants']

        if not phrases:
            print(f"    Upozornění: Kategorie '{category_name}' nemá žádné fráze (kotva+pozitivní) pro výpočet centroidu. Přeskakuji.")
            continue

        category_index_map[category_name] = (current_embedding_index, current_embedding_index + len(phrases))
        all_phrases_for_encoding.extend(phrases)
        current_embedding_index += len(phrases)

    if not all_phrases_for_encoding:
        print("Nenalezeny žádné fráze pro výpočet centroidů.")
        return None

    # Zakódujeme všechny fráze najednou
    print(f"  Zakódovávám {len(all_phrases_for_encoding)} frází pro výpočet centroidů...")
    # Use a more memory-efficient batch size, especially for MPS
    encode_batch_size = 32 if device == 'mps' else 64
    all_embeddings = encode_texts(model, all_phrases_for_encoding, device, batch_size=encode_batch_size)
    print("  Kódování dokončeno.")


    # Vypočítáme průměrné embeddingy (centroidy) pro každou kategorii podle mapování indexů
    category_centroids = {}
    for category_name, (start_idx, end_idx) in category_index_map.items():
        if start_idx >= end_idx:  # Prázdný rozsah
            continue

        category_embeddings = all_embeddings[start_idx:end_idx]
        # Vypočítáme průměr embeddingů podél osy 0 (průměr přes jednotlivé embedding vektory)
        centroid = np.mean(category_embeddings, axis=0)
        # Normalizujeme centroid, aby kosinusová podobnost fungovala správně
        centroid = centroid / np.linalg.norm(centroid)
        category_centroids[category_name] = centroid

    print(f"Výpočet centroidů dokončen pro {len(category_centroids)} kategorií.")

    # Uložení centroidů na disk, pokud je zadána cesta
    if save_path and category_centroids:
        save_centroids(category_centroids, {}, save_path)

    return category_centroids


def save_centroids(category_centroids, category_id_mapping, save_path):
    """
    Uloží vypočtené centroidy kategorií do souboru pro pozdější použití.

    Args:
        category_centroids (dict): Slovník centroidů kategorií
        category_id_mapping (dict): Mapování kategorií na ID
        save_path (str): Cesta pro uložení centroidů. Může být cesta k souboru nebo adresáři.
                         Pokud je to adresář, soubor se vytvoří jako 'centroids.pkl' v tomto adresáři.
    """
    if category_centroids is None or not category_centroids:
        print("Chyba: Centroidy kategorií nejsou vypočítány. Nelze uložit.")
        return

    # Kontrola, zda je save_path adresář nebo soubor
    if os.path.isdir(save_path) or save_path.endswith('/'):
        os.makedirs(save_path, exist_ok=True)
        save_path = os.path.join(save_path, 'centroids.pkl')
    else:
        # Vytvoření adresáře, pokud neexistuje
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)

    try:
        # Uložíme jak centroidy, tak mapování kategorií
        data_to_save = {
            'centroids': category_centroids,
            'category_id_mapping': category_id_mapping
        }
        with open(save_path, 'wb') as f:
            pickle.dump(data_to_save, f)
        print(f"Centroidy a mapování kategorií úspěšně uloženy do: {save_path}")
    except Exception as e:
        print(f"Chyba při ukládání centroidů: {str(e)}")


def fine_tune_model(data_dir, model_dir=None, num_epochs=5, train_batch_size=16,
                   learning_rate=2e-5, save_centroids_path=None, validation_split=0.2, print_triplets=False):
    """
    Provede fine-tuning modelu na datech načtených z XLSX souborů v zadaném adresáři.
    Použije lokální model, pokud existuje, jinak výchozí.
    Implementuje Early Stopping pomocí Evaluatoru a save_best_model.

    Args:
        model_name (str): Název modelu pro stažení z externích zdrojů.
        data_dir (str): Cesta k adresáři obsahujícímu XLSX soubor s tréninkovými daty.
        model_dir (str): Cesta k adresáři, kde je/bude uložen nejlepší model.
        num_epochs (int): Maximální počet epoch pro trénink (Early Stopping může zastavit dříve).
        train_batch_size (int): Velikost dávky pro trénink i validaci.
        learning_rate (float): Učící rychlost.
        save_centroids_path (str, optional): Cesta pro uložení vypočtených centroidů. Pokud None, použije se model_dir.
        validation_split (float): Podíl připravených tripletů, který bude použit pro validaci (0.0 až 1.0).
        print_triplets (bool): Pokud True, vypíše všechny vytvořené triplety pro kontrolu tréninku.

    Returns:
         list: Strukturovaná data načtená z XLSX souborů.
    """
    # Detekce zařízení
    device = detect_device()
    print(f"Používám zařízení: {device}")

    # Pokud není zadán model_dir, použijeme výchozí
    if model_dir is None:
        model_dir = "mpnet"

    # Vytvoříme adresář pro model, pokud neexistuje - je potřeba pro save_best_model=True
    os.makedirs(model_dir, exist_ok=True)

    # Pokud není zadána cesta pro uložení centroidů, použijeme model_dir
    if save_centroids_path is None:
        save_centroids_path = os.path.join(model_dir, "centroids")

    # Načteme nebo vytvoříme model
    model = KeyClassifier()


    print("Spouštím fine-tuning...")

    # Načteme data z XLSX souboru (s novou strukturou)
    class_info, structured_data = load_training_data_from_xlsx(data_dir)

    if not structured_data:
        print("Nenalezena žádná platná tréninková data. Fine-tuning zrušen.")
        return []

    # Připravíme data jako flat list tripletů [ankr, pozitivní, negativní]
    all_examples = prepare_training_data(structured_data, print_triplets=print_triplets)

    if not all_examples:
        print("Z tréninkových dat nebyly vytvořeny žádné triplety InputExample. Fine-tuning zrušen.")
        # I když není dost tripletů pro trénink, můžeme zkusit spočítat centroidy z načtených dat
        calculate_category_centroids(model, structured_data, device, save_centroids_path)
        return structured_data

    # Rozdělení dat na trénovací a validační sadu
    train_examples = all_examples
    val_examples = None
    evaluator = None

    if validation_split > 0 and validation_split < 1.0:
        print(f"Rozděluji data na trénovací a validační sadu (validační podíl: {validation_split})")
        val_size = int(len(all_examples) * validation_split)
        train_size = len(all_examples) - val_size

        if val_size == 0:
             print("    Validační sada by měla 0 příkladů, Early Stopping nebude použit.")
             train_examples = all_examples
             val_examples = None # Explicitně nastavíme na None
        else:
            # Použijeme random_split pro rozdělení dat
            # Poznámka: random_split může vést k nerovnoměrnému rozložení kategorií v malé sadě
            train_examples, val_examples = random_split(
                all_examples,
                [train_size, val_size],
                generator=torch.Generator().manual_seed(42)  # Pro reprodukovatelnost
            )
            print(f"Trénovací sada: {len(train_examples)} příkladů, Validační sada: {len(val_examples)} příkladů")
    else:
        train_examples = all_examples
        val_examples = None # Explicitně nastavíme na None
        print(f"Používám všechna data pro trénink: {len(train_examples)} příkladů. Early Stopping nebude použit.")

    # Set pin_memory=False explicitly for MPS compatibility
    # Reduce batch size for MPS to avoid memory issues
    actual_batch_size = train_batch_size
    if device == 'mps':
        # Use a smaller batch size on MPS to avoid memory issues
        actual_batch_size = min(train_batch_size, 32)  # Limit batch size on MPS
        print(f"Using reduced batch size {actual_batch_size} for MPS device (original: {train_batch_size})")
    elif device == 'cuda':
         # Můžete zde zkusit povolit mixed precision pro CUDA, pokud ji váš hardware podporuje
         print("Zvažte použití mixed precision training (FP16) pro úsporu paměti na CUDA.")

    train_dataloader = DataLoader(
        train_examples,
        shuffle=True,
        batch_size=actual_batch_size,
        pin_memory=(device != 'mps')
    )

    # Vytvoříme evaluator, pokud máme validační data
    if val_examples is not None:
        # Extrahujeme data z validačních příkladů pro TripletEvaluator
        # Pro triplet data potřebujeme kotvy, pozitivní a negativní příklady
        anchors = []
        positives = []
        negatives = []

        # Projdeme všechny validační příklady a extrahujeme data
        for example in val_examples:
            # InputExample má atribut texts, který obsahuje [kotva, pozitivní, negativní]
            anchors.append(example.texts[0])
            positives.append(example.texts[1])
            negatives.append(example.texts[2])

        # Vytvoříme TripletEvaluator, který správně vyhodnotí triplet loss trénink
        # Měří přesnost tripletů: zda je distance(anchor, positive) < distance(anchor, negative) + margin
        evaluator = TripletEvaluator(
            anchors=anchors,
            positives=positives,
            negatives=negatives,
            name="validation"
        )
        print(f"Připraven TripletEvaluator pro Early Stopping s {len(anchors)} triplety.")

    train_loss = losses.TripletLoss(model=model.model)

    # Nastavení pro model.fit
    fit_params = {
        'train_objectives': [(train_dataloader, train_loss)],
        'epochs': num_epochs,
        # Warmup steps by default is 10% of total training steps
        'warmup_steps': int(len(train_dataloader) * num_epochs * 0.1),
        'output_path': model_dir, # Zde se uloží NEJLEPŠÍ model, pokud je evaluator a save_best_model=True
        'save_best_model': True, # Povolíme ukládání nejlepšího modelu (aktivuje Early Stopping s Evaluator)
        'evaluator': evaluator, # Předáme připravený evaluátor
        'optimizer_params': {'lr': learning_rate},
        'show_progress_bar': True,
        'optimizer_class': torch.optim.AdamW
        # 'scheduler': 'WarmupLinear'

    }

    # Pokud nemáme validační sadu/evaluator, vypneme ukládání nejlepšího modelu (není podle čeho ho vybrat)
    if evaluator is None:
         fit_params['save_best_model'] = False
         print("Early Stopping není aktivní, trénink poběží po celý počet epoch.")

    # Informace o tréninku
    print(f"\nSpouštím trénink s learning rate: {learning_rate}, batch size: {actual_batch_size}")

    # Spustíme trénink
    model.model.fit(**fit_params)

    print("Fine-tuning dokončen (nebo zastaven Early Stoppingem).")
    print(f"Výsledný model (nejlepší nebo poslední) je uložen v: {model_dir}")

    # Po dokončení fit načteme nejlepší model a spočítáme centroidy pomocí metod KeyClassifier
    print(f"Načítám nejlepší (nebo poslední) uložený model z {model_dir} pro výpočet centroidů.")
    try:
        # Načteme nejlepší model přímo - KeyClassifier načte model z model_dir
        model = KeyClassifier(model_dir=model_dir, device=device)
        print("Nejlepší model úspěšně načten pro výpočet centroidů.")
    except Exception as e:
        print(f"Chyba při načítání výsledného modelu: {e}")
        print("Používám aktuální model instance pro výpočet centroidů.")

    # Vždy použijeme metody KeyClassifier pro výpočet a uložení centroidů
    print("Počítám centroidy na základě trénovacích dat a výsledného modelu.")
    model.set_class_prototypes(structured_data)
    prototypes_file = os.path.join(model_dir, 'prototypes.bin')
    model.save_prototypes_binary(prototypes_file)
    print(f"Centroidy uloženy do: {prototypes_file}")

    return structured_data # Vrátíme načtená data pro případné další použití


if __name__ == "__main__":
    """
    Příklad použití standalone fine-tuning funkce.
    """
    # Nastavení pro Apple Silicon MPS backend
    os.environ["TOKENIZERS_PARALLELISM"] = "false"

    # Parametry fine-tuningu
    data_dir = 'training_data'
    #model_dir = 'mpnet'
    batch_size = 32

    print("🚀 Spouštím standalone fine-tuning...")

    structured_data = fine_tune_model(
        data_dir=data_dir,
        #model_dir=model_dir,
        print_triplets=False,
        num_epochs=3,
        train_batch_size=batch_size,
        learning_rate=2e-5,
        validation_split=0.2
    )

    print(f"\n{'='*60}")
    print("Fine-tuning dokončen!")
    print(f"Model uložen")
    print(f"Načteno {len(structured_data)} kategorií z trénovacích dat.")
