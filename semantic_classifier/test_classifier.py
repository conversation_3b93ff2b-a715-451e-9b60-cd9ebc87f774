#!/usr/bin/env python3
"""
Test script pro novou zjednodušenou třídu Classifier.
"""

import os
import sys
import pandas as pd

# Přidáme cestu k utils modulu
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from KeyClassifier import KeyClassifier



def test_classifier_initialization():
    """Test inicializace klasifikátoru."""
    print("=== Test inicializace klasifikátoru ===")
    
    try:
        # Test s existujícím modelem
        classifier = KeyClassifier()
        print("✓ Klasifikátor úspěšně inicializován")
        
        # Test základních vlastností
        print(f"Device: {classifier.device}")
        print(f"Model name: {classifier.model_name}")
        print(f"Model dir: {classifier.model_dir}")
        
        return classifier
        
    except Exception as e:
        print(f"❌ Chyba při inicializaci: {e}")
        return None


def test_preprocessing():
    """Test preprocessing funkce."""
    print("\n=== Test preprocessing ===")
    
    classifier = KeyClassifier()
    
    test_texts = [
        "Číslo faktury",
        "Datum.vystavení",
        "Text s,čárkami;a:dvojtečkami",
        "Text   s    více    mezerami",
        ""
    ]
    
    for text in test_texts:
        processed = classifier.classify(text)
        print(f"'{text}' -> '{processed}'")
    
    print("✓ Preprocessing test dokončen")


def test_encoding():
    """Test kódování textů."""
    print("\n=== Test kódování textů ===")
    
    classifier = Classifier()
    
    test_texts = [
        "Číslo faktury",
        "Datum vystavení",
        "Celková částka"
    ]
    
    try:
        embeddings = classifier.encode(test_texts)
        print(f"✓ Zakódováno {len(test_texts)} textů")
        print(f"Rozměr embeddingů: {embeddings.shape}")
        
        # Test jednotlivého textu
        single_embedding = classifier.encode("Test text")
        print(f"✓ Zakódován jednotlivý text, rozměr: {single_embedding.shape}")
        
    except Exception as e:
        print(f"❌ Chyba při kódování: {e}")


def test_model_operations():
    """Test operací s modelem."""
    print("\n=== Test operací s modelem ===")
    
    classifier = KeyClassifier()
    
    # Test uložení modelu
    test_model_dir = "test_model_save"
    try:
        success = classifier.save_model(test_model_dir, save_centroids=False)
        if success:
            print("✓ Model úspěšně uložen")
            
            # Test načtení modelu
            success = classifier.load_model(test_model_dir, load_centroids=False)
            if success:
                print("✓ Model úspěšně načten")
            else:
                print("❌ Chyba při načítání modelu")
        else:
            print("❌ Chyba při ukládání modelu")
            
    except Exception as e:
        print(f"❌ Chyba při operacích s modelem: {e}")
    
    # Vyčištění
    try:
        import shutil
        if os.path.exists(test_model_dir):
            shutil.rmtree(test_model_dir)
            print("✓ Testovací složka vyčištěna")
    except:
        pass


def test_category_mapping():
    """Test mapování kategorií."""
    print("\n=== Test mapování kategorií ===")
    
    classifier = KeyClassifier()
    
    # Test nastavení mapování
    test_mapping = {
        "Číslo faktury": 1,
        "Datum vystavení": 2,
        "Celková částka": 3
    }
    
    try:
        classifier.set_category_id_mapping(test_mapping)
        print("✓ Mapování kategorií nastaveno")
        
        # Test získání ID kategorií
        for category_name, expected_id in test_mapping.items():
            actual_id = classifier.get_category_id(category_name)
            if actual_id == expected_id:
                print(f"✓ {category_name} -> {actual_id}")
            else:
                print(f"❌ {category_name} -> {actual_id} (očekáváno {expected_id})")
        
        # Test neznámé kategorie
        unknown_id = classifier.get_category_id("Neznámá kategorie")
        if unknown_id == 0:
            print("✓ Neznámá kategorie -> 0")
        else:
            print(f"❌ Neznámá kategorie -> {unknown_id} (očekáváno 0)")
            
    except Exception as e:
        print(f"❌ Chyba při testování mapování: {e}")


def test_batch_classify():
    """Test batch klasifikace."""
    print("\n=== Test batch klasifikace ===")
    
    classifier = KeyClassifier()
    
    # Vytvoříme testovací DataFrame
    test_data = {
        'text': [
            'Číslo faktury',
            'Datum vystavení', 
            'Celková částka',
            '12345',  # Text s číslicemi - měl by být přeskočen
            'Dodavatel'
        ]
    }
    
    df = pd.DataFrame(test_data)
    
    try:
        # Test bez centroidů (měl by vrátit původní DataFrame)
        result_df = classifier.batch_classify(df, text_column='text', class_column='predicted_class')
        print("✓ Batch klasifikace proběhla (bez centroidů)")
        print(f"Výsledný DataFrame má {len(result_df)} řádků")
        
    except Exception as e:
        print(f"❌ Chyba při batch klasifikaci: {e}")


def main():
    """Hlavní testovací funkce."""
    print("🧪 Spouštím testy pro zjednodušenou třídu Classifier")
    print("=" * 60)
    
    # Spustíme všechny testy
    classifier = test_classifier_initialization()
    if classifier is None:
        print("❌ Inicializace selhala, ukončuji testy")
        return
    
    test_preprocessing()
    test_encoding()
    test_model_operations()
    test_category_mapping()
    test_batch_classify()
    
    print("\n" + "=" * 60)
    print("🎉 Všechny testy dokončeny!")
    print("\nPoznámka: Pro plné testování klasifikace je potřeba:")
    print("1. Vytvořit training_set.xlsx s tréninkovými daty")
    print("2. Spustit fine_tune() pro natrénování modelu")
    print("3. Poté testovat classify() a batch_classify() s centroidy")


if __name__ == "__main__":
    main()
