#!/usr/bin/env python3
"""
Test edge cases pro opravu sestavování tripletů.
Testuje specifické situace, kdy chy<PERSON><PERSON> jeden typ problematick<PERSON>ch přík<PERSON>.
"""

import os
import numpy as np
from sentence_transformers import SentenceTransformer

from fine_tune_extended import (
    select_problematic_examples,
    calculate_similarity_scores,
    POSITIVE_THRESHOLD,
    NEGATIVE_THRESHOLD
)
from fine_tune_standalone import detect_device


def create_edge_case_data():
    """
    Vytvoří testovací data pro edge cases:
    1. <PERSON><PERSON><PERSON><PERSON> s pouze problematickými pozitivy (negativa jsou dobrá)
    2. <PERSON><PERSON><PERSON><PERSON> s pouze problematickými negativy (pozitiva jsou dobrá)
    """
    # Použijeme texty, které budou mít předvídatelné skóre
    test_data = [
        {
            'category_name': 'EdgeCase_OnlyBadPositives',
            'anchor_triplet': 'invoice document payment',
            'positives_variants': ['completely different text', 'unrelated content'],  # N<PERSON>zk<PERSON> skóre
            'negatives': ['not invoice not document not payment', 'different topic entirely']  # <PERSON><PERSON>zk<PERSON> skóre (dobré negativa)
        },
        {
            'category_name': 'EdgeCase_OnlyBadNegatives', 
            'anchor_triplet': 'contract agreement legal',
            'positives_variants': ['contract agreement legal document', 'legal contract agreement'],  # Vysoké skóre
            'negatives': ['contract agreement legal similar', 'legal agreement contract text']  # Vysoké skóre (špatná negativa)
        }
    ]
    return test_data


def test_edge_cases():
    """
    Testuje edge cases pro sestavování tripletů.
    """
    print("🧪 TESTOVÁNÍ EDGE CASES PRO SESTAVOVÁNÍ TRIPLETŮ")
    print("=" * 60)
    
    # Detekce zařízení
    device = detect_device()
    print(f"🖥️  Používám zařízení: {device}")
    
    # Načteme model
    model_name = 'sentence-transformers/paraphrase-multilingual-mpnet-base-v2'
    print(f"📥 Načítám model: {model_name}")
    model = SentenceTransformer(model_name, device=device)
    
    # Vytvoříme testovací data
    test_data = create_edge_case_data()
    print(f"📊 Vytvořeno {len(test_data)} edge case testů")
    
    print("\n" + "=" * 60)
    print("🔍 ANALÝZA EDGE CASES")
    print("=" * 60)
    
    results = {}
    
    # Analyzujeme každou třídu jednotlivě
    for item in test_data:
        category_name = item['category_name']
        anchor_text = item['anchor_triplet']
        positives = item['positives_variants']
        negatives = item['negatives']
        
        print(f"\n📋 Třída: {category_name}")
        print(f"   Kotva: '{anchor_text}'")
        print(f"   Pozitiva: {positives}")
        print(f"   Negativa: {negatives}")
        
        # Vypočítáme skóre
        positive_scores = calculate_similarity_scores(model, anchor_text, positives, device)
        negative_scores = calculate_similarity_scores(model, anchor_text, negatives, device)
        
        print(f"   Skóre pozitiv: {positive_scores}")
        print(f"   Skóre negativ: {negative_scores}")
        
        # Zkontrolujeme problematické příklady
        problematic_positives = np.sum(positive_scores < POSITIVE_THRESHOLD)
        problematic_negatives = np.sum(negative_scores > NEGATIVE_THRESHOLD)
        
        print(f"   Problematická pozitiva: {problematic_positives}/{len(positives)} (threshold < {POSITIVE_THRESHOLD})")
        print(f"   Problematická negativa: {problematic_negatives}/{len(negatives)} (threshold > {NEGATIVE_THRESHOLD})")
        
        results[category_name] = {
            'problematic_positives': problematic_positives,
            'problematic_negatives': problematic_negatives,
            'positive_scores': positive_scores,
            'negative_scores': negative_scores
        }
    
    print("\n" + "=" * 60)
    print("🔧 TEST NOVÉ LOGIKY NA EDGE CASES")
    print("=" * 60)
    
    # Testujeme novou logiku
    triplets, stats = select_problematic_examples(model, test_data, device, max_examples_per_class=3)
    
    print(f"\n📊 VÝSLEDKY EDGE CASE TESTŮ:")
    print(f"   Celkem tripletů vytvořeno: {len(triplets)}")
    print(f"   Statistiky:")
    for key, value in stats.items():
        print(f"      {key}: {value}")
    
    # Ověříme specifické edge cases
    success = True
    
    # Test 1: EdgeCase_OnlyBadPositives - měl by vytvořit triplety i když negativa jsou dobrá
    case1 = results['EdgeCase_OnlyBadPositives']
    if case1['problematic_positives'] > 0 and case1['problematic_negatives'] == 0:
        print(f"✅ Edge Case 1 správně detekován: pouze problematická pozitiva")
        if len(triplets) > 0:
            print(f"✅ Triplety byly vytvořeny i přes dobrá negativa")
        else:
            print(f"❌ Triplety nebyly vytvořeny!")
            success = False
    else:
        print(f"⚠️  Edge Case 1 neodpovídá očekávání")
    
    # Test 2: EdgeCase_OnlyBadNegatives - měl by vytvořit triplety i když pozitiva jsou dobrá  
    case2 = results['EdgeCase_OnlyBadNegatives']
    if case2['problematic_negatives'] > 0 and case2['problematic_positives'] == 0:
        print(f"✅ Edge Case 2 správně detekován: pouze problematická negativa")
        if len(triplets) > 0:
            print(f"✅ Triplety byly vytvořeny i přes dobrá pozitiva")
        else:
            print(f"❌ Triplety nebyly vytvořeny!")
            success = False
    else:
        print(f"⚠️  Edge Case 2 neodpovídá očekávání")
    
    return success


def test_forced_selection():
    """
    Testuje, že se správně vybírají nejhorší příklady při doplňování.
    """
    print("\n" + "=" * 60)
    print("🔧 TEST VÝBĚRU NEJHORŠÍCH PŘÍKLADŮ")
    print("=" * 60)
    
    # Detekce zařízení
    device = detect_device()
    model = SentenceTransformer('sentence-transformers/paraphrase-multilingual-mpnet-base-v2', device=device)
    
    # Vytvoříme data s gradovanými skóre
    test_data = [{
        'category_name': 'GradedTest',
        'anchor_triplet': 'test anchor',
        'positives_variants': ['very good positive', 'medium positive', 'bad positive'],  # Očekáváme klesající skóre
        'negatives': ['very bad negative', 'medium negative', 'good negative']  # Očekáváme klesající skóre
    }]
    
    item = test_data[0]
    anchor_text = item['anchor_triplet']
    positives = item['positives_variants']
    negatives = item['negatives']
    
    # Vypočítáme skóre
    positive_scores = calculate_similarity_scores(model, anchor_text, positives, device)
    negative_scores = calculate_similarity_scores(model, anchor_text, negatives, device)
    
    print(f"📊 Skóre pozitiv: {positive_scores}")
    print(f"📊 Skóre negativ: {negative_scores}")
    
    # Najdeme nejhorší příklady
    worst_positive_idx = np.argmin(positive_scores)
    worst_negative_idx = np.argmax(negative_scores)
    
    print(f"🎯 Nejhorší pozitivum: index {worst_positive_idx}, skóre {positive_scores[worst_positive_idx]:.3f}")
    print(f"🎯 Nejhorší negativum: index {worst_negative_idx}, skóre {negative_scores[worst_negative_idx]:.3f}")
    
    print("✅ Test výběru nejhorších příkladů dokončen")
    return True


if __name__ == "__main__":
    """
    Hlavní test funkce pro edge cases.
    """
    print("🚀 SPOUŠTÍM EDGE CASE TESTY PRO OPRAVU TRIPLETŮ")
    print("=" * 60)
    
    try:
        # Test edge cases
        success1 = test_edge_cases()
        
        # Test výběru nejhorších příkladů
        success2 = test_forced_selection()
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("🎉 VŠECHNY EDGE CASE TESTY ÚSPĚŠNÉ!")
            print("✅ Oprava sestavování tripletů správně řeší všechny edge cases")
        else:
            print("❌ NĚKTERÉ EDGE CASE TESTY SELHALY!")
            print("   Zkontrolujte implementaci")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ CHYBA PŘI EDGE CASE TESTOVÁNÍ:")
        print(f"   {str(e)}")
        raise
