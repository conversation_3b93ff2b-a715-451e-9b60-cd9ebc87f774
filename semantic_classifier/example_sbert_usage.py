#!/usr/bin/env python3
"""
Příklad použití SBertClassifier s reá<PERSON><PERSON><PERSON> daty z registrů.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_classifier.SBertClassifier import SBertClassifier
from utils.utils import KEY_CLASS_REGISTRY

def create_training_data_from_registry():
    """Vytvoří trénovací data z KEY_CLASS_REGISTRY."""
    
    # Základní trénovací texty pro každou kategorii
    training_data = {
        'Stránka': ['stránka', 'page', 'str.', 'strana'],
        'Faktura': ['faktura', 'invoice', 'účet', 'bill', 'faktúra'],
        'Dobropis': ['dobropis', 'credit note', 'dobropis', 'kredit'],
        '<PERSON>álohová faktura': ['z<PERSON><PERSON>hov<PERSON> faktura', 'advance invoice', 'záloha', 'advance payment'],
        'Dodací list': ['dodací list', 'delivery note', 'dodávka', 'delivery'],
        'Objednávka': ['objednávka', 'order', 'purchase order', 'PO'],
        'Číslo faktury': ['číslo faktury', 'invoice number', 'invoice no', 'č. faktury', 'faktura č.'],
        'Číslo dodacího listu': ['číslo dodacího listu', 'delivery note number', 'dodací list č.'],
        'Číslo objednávky': ['číslo objednávky', 'order number', 'objednávka č.', 'order no'],
        'Variabilní symbol': ['variabilní symbol', 'variable symbol', 'VS', 'var. symbol'],
        'Dodavatel': ['dodavatel', 'supplier', 'vendor', 'prodávající'],
        'Odběratel': ['odběratel', 'customer', 'buyer', 'kupující'],
        'IČO': ['IČO', 'IČ', 'identifikační číslo', 'company ID', 'reg. number'],
        'DIČ': ['DIČ', 'daňové identifikační číslo', 'tax ID', 'VAT ID'],
        'Datum vystavení': ['datum vystavení', 'issue date', 'date of issue', 'vystaveno'],
        'Datum splatnosti': ['datum splatnosti', 'due date', 'splatnost', 'splatné'],
        'DUZP': ['DUZP', 'datum uskutečnění zdanitelného plnění', 'tax point'],
        'Číslo účtu': ['číslo účtu', 'account number', 'účet', 'account no'],
        'Kód banky': ['kód banky', 'bank code', 'banka'],
        'IBAN': ['IBAN', 'mezinárodní číslo účtu'],
        'Sleva': ['sleva', 'discount', 'rabat', 'zľava'],
        'Záloha': ['záloha', 'advance', 'deposit', 'advance payment'],
        'Celkem': ['celkem', 'total', 'suma', 'together'],
        'Celkem k úhradě': ['celkem k úhradě', 'total to pay', 'k úhradě', 'amount due'],
        'Celkem s DPH': ['celkem s DPH', 'total with VAT', 'včetně DPH', 'with tax']
    }
    
    return training_data

def main():
    """Hlavní funkce demonstrující použití."""
    print("=== SBertClassifier s reálnými daty ===")
    
    # 1. Inicializace
    classifier = SBertClassifier()
    
    # 2. Vytvoření prototypů z registru
    print("\nVytvářím prototypy z KEY_CLASS_REGISTRY...")
    training_data = create_training_data_from_registry()
    classifier.create_and_save_prototypes(training_data)
    
    # 3. Test na reálných textech z faktur
    print("\nTest klasifikace reálných textů:")
    test_texts = [
        "Faktura č. 2024001",
        "Invoice number: INV-2024-001", 
        "Datum vystavení: 15.01.2024",
        "Issue date: 2024-01-15",
        "Celkem k úhradě: 15 000 Kč",
        "Total amount: 15,000 CZK",
        "IČO: 12345678",
        "Company ID: 87654321",
        "Variabilní symbol: 2024001",
        "IBAN: CZ65 0800 0000 1920 0014 5399",
        "Dodavatel: ABC s.r.o.",
        "Supplier: XYZ Ltd.",
        "Datum splatnosti: 29.01.2024",
        "Due date: 2024-01-29",
        "Sleva 5%",
        "Discount 10%"
    ]
    
    # Jednotlivá klasifikace
    print("\n--- Jednotlivá klasifikace ---")
    for text in test_texts:
        class_id, confidence = classifier.classify(text)
        class_name = classifier.get_class_name(class_id)
        print(f"'{text}' -> {class_name} (confidence: {confidence:.3f})")
    
    # Batch klasifikace
    print("\n--- Batch klasifikace ---")
    batch_results = classifier.classify_batch(test_texts)
    for text, (class_id, confidence) in zip(test_texts, batch_results):
        class_name = classifier.get_class_name(class_id)
        if confidence > 0.5:  # Pouze vysoké confidence
            print(f"✅ '{text}' -> {class_name} (confidence: {confidence:.3f})")
        else:
            print(f"❓ '{text}' -> {class_name} (confidence: {confidence:.3f}) [nízká jistota]")
    
    # 4. Test podobnosti
    print("\n--- Test podobnosti ---")
    similarity_tests = [
        ("faktura", "invoice"),
        ("datum vystavení", "issue date"),
        ("celkem", "total"),
        ("IČO", "company ID"),
        ("dodavatel", "supplier")
    ]
    
    for text1, text2 in similarity_tests:
        similarity = classifier.similarity(text1, text2)
        print(f"Podobnost '{text1}' vs '{text2}': {similarity:.3f}")
    
    # 5. Informace o modelu
    print("\n--- Informace o modelu ---")
    classifier.info()
    
    # 6. Export pro C++
    print("\n--- Export pro C++ ---")
    cpp_export_path = "./semantic_classifier/mpnet_ok/sbert_prototypes.bin"
    classifier.export_for_cpp(cpp_export_path)
    
    # Zobrazíme velikost exportovaného souboru
    if os.path.exists(cpp_export_path):
        size_kb = os.path.getsize(cpp_export_path) / 1024
        print(f"Velikost exportovaného souboru: {size_kb:.1f} KB")
    
    print("\n✅ Demo dokončeno!")

if __name__ == "__main__":
    main()
